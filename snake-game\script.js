/**
 * 贪吃蛇游戏 - 主要游戏逻辑
 * 使用ES6+语法和面向对象编程
 */

// 游戏配置常量
const GAME_CONFIG = {
    CANVAS_SIZE: 600,
    GRID_SIZE: 20,
    INITIAL_SNAKE_LENGTH: 3,
    SPEEDS: {
        easy: 150,
        medium: 100,
        hard: 60
    },
    FOOD_TYPES: {
        NORMAL: 'normal',
        GOLDEN: 'golden',
        DIAMOND: 'diamond'
    },
    DIRECTIONS: {
        UP: { x: 0, y: -1 },
        DOWN: { x: 0, y: 1 },
        LEFT: { x: -1, y: 0 },
        RIGHT: { x: 1, y: 0 }
    },
    GAME_MODES: {
        CLASSIC: 'classic',
        TIME_ATTACK: 'time-attack',
        SURVIVAL: 'survival',
        SPEED_RUN: 'speed-run',
        ZEN: 'zen',
        CHAOS: 'chaos'
    }
};

// 主题配置
const THEMES = {
    neon: {
        name: '霓虹',
        colors: {
            snake: '#FFB6C1',      // 淡粉色
            snakeHead: '#ADD8E6',  // 淡蓝色
            food: '#F0B6E6',       // 淡粉紫色
            specialFood: {
                golden: '#F5DEB3',  // 淡金色
                diamond: '#E0F6FF'  // 淡天蓝色
            },
            obstacle: '#E6E6FA',   // 淡薰衣草色
            background: '#1E1E2E'  // 深蓝灰背景
        },
        effects: {
            glow: true,
            particles: true,
            trail: true
        }
    },
    classic: {
        name: '经典',
        colors: {
            snake: '#228B22',
            snakeHead: '#32CD32',
            food: '#DC143C',
            specialFood: {
                golden: '#DAA520',  // Goldenrod instead of gold
                diamond: '#4169E1'
            },
            obstacle: '#654321',  // Dark brown instead of saddle brown
            background: '#F5F5DC'
        },
        effects: {
            glow: false,
            particles: false,
            trail: false
        }
    },
    forest: {
        name: '森林',
        colors: {
            snake: '#2E8B57',  // Sea green instead of forest green
            snakeHead: '#3CB371',  // Medium sea green instead of lime green
            food: '#FF4500',
            specialFood: {
                golden: '#B8860B',  // Dark goldenrod instead of gold
                diamond: '#48D1CC'  // Medium turquoise instead of dark turquoise
            },
            obstacle: '#8B4513',  // Keep original brown for forest theme
            background: '#0B2F0B'
        },
        effects: {
            glow: true,
            particles: true,
            trail: false
        }
    },
    ocean: {
        name: '海洋',
        colors: {
            snake: '#40E0D0',  // Turquoise instead of dark turquoise
            snakeHead: '#20B2AA',
            food: '#FF6347',
            specialFood: {
                golden: '#F0E68C',  // Khaki instead of gold
                diamond: '#4682B4'
            },
            obstacle: '#2F4F4F',
            background: '#001122'
        },
        effects: {
            glow: true,
            particles: true,
            trail: true
        }
    },
    sunset: {
        name: '日落',
        colors: {
            snake: '#FF6B35',
            snakeHead: '#F7931E',
            food: '#FFD23F',
            specialFood: {
                golden: '#FFA500',  // Orange instead of gold
                diamond: '#FF69B4'  // Hot pink instead of deep pink
            },
            obstacle: '#A0522D',  // Sienna instead of saddle brown
            background: '#1A0F0A'
        },
        effects: {
            glow: true,
            particles: true,
            trail: true
        }
    },
    cyberpunk: {
        name: '赛博朋克',
        colors: {
            snake: '#FF00FF',
            snakeHead: '#8A2BE2',
            food: '#00FFFF',
            specialFood: {
                golden: '#ADFF2F',  // Green yellow instead of yellow
                diamond: '#DC143C'  // Crimson instead of deep pink
            },
            obstacle: '#696969',
            background: '#000000'
        },
        effects: {
            glow: true,
            particles: true,
            trail: true
        }
    }
};

// 游戏模式配置
const GAME_MODE_CONFIG = {
    classic: {
        name: '经典模式',
        description: '传统的贪吃蛇游戏，吃食物增长，避免撞墙和自己',
        timeLimit: null,
        specialRules: {},
        scoreMultiplier: 1,
        icon: '🐍'
    },
    'time-attack': {
        name: '时间挑战',
        description: '在限定时间内获得最高分数',
        timeLimit: 120, // 2分钟
        specialRules: {
            showTimer: true,
            bonusFood: true,
            fastFood: true
        },
        scoreMultiplier: 1.5,
        icon: '⏰'
    },
    survival: {
        name: '生存模式',
        description: '障碍物会随机出现，尽可能长时间生存',
        timeLimit: null,
        specialRules: {
            dynamicObstacles: true,
            obstacleSpawnRate: 0.02,
            maxObstacles: 15,
            noWalls: false
        },
        scoreMultiplier: 1.2,
        icon: '💀'
    },
    'speed-run': {
        name: '竞速模式',
        description: '速度会逐渐增加，挑战你的反应极限',
        timeLimit: null,
        specialRules: {
            speedIncrease: true,
            speedIncreaseRate: 0.98,
            minSpeed: 30
        },
        scoreMultiplier: 2,
        icon: '⚡'
    },
    zen: {
        name: '禅意模式',
        description: '无边界，无死亡，纯粹的放松体验',
        timeLimit: null,
        specialRules: {
            noWalls: true,
            noDeath: true,
            peacefulMusic: true,
            slowSpeed: true
        },
        scoreMultiplier: 0.5,
        icon: '🧘'
    },
    chaos: {
        name: '混沌模式',
        description: '随机事件不断发生，充满惊喜和挑战',
        timeLimit: null,
        specialRules: {
            randomEvents: true,
            eventInterval: 10, // 每10秒一个事件
            multipleFood: true,
            teleportFood: true,
            sizeChanges: true
        },
        scoreMultiplier: 1.8,
        icon: '🌪️'
    }
};

/**
 * 游戏模式管理器
 */
class GameModeManager {
    constructor() {
        this.currentMode = 'classic';
        this.modeStartTime = 0;
        this.eventTimer = 0;
        this.lastEventTime = 0;
        this.activeEvents = [];
    }

    /**
     * 设置游戏模式
     */
    setMode(modeName) {
        if (!GAME_MODE_CONFIG[modeName]) {
            console.warn('游戏模式不存在:', modeName);
            return;
        }

        this.currentMode = modeName;
        this.modeStartTime = Date.now();
        this.eventTimer = 0;
        this.lastEventTime = 0;
        this.activeEvents = [];

        console.log('游戏模式已切换到:', GAME_MODE_CONFIG[modeName].name);
    }

    /**
     * 获取当前模式配置
     */
    getCurrentModeConfig() {
        return GAME_MODE_CONFIG[this.currentMode];
    }

    /**
     * 检查时间限制
     */
    checkTimeLimit() {
        const config = this.getCurrentModeConfig();
        if (!config.timeLimit) return false;

        const elapsed = (Date.now() - this.modeStartTime) / 1000;
        return elapsed >= config.timeLimit;
    }

    /**
     * 获取剩余时间
     */
    getRemainingTime() {
        const config = this.getCurrentModeConfig();
        if (!config.timeLimit) return null;

        const elapsed = (Date.now() - this.modeStartTime) / 1000;
        return Math.max(0, config.timeLimit - elapsed);
    }

    /**
     * 更新模式特殊逻辑
     */
    update(game) {
        const config = this.getCurrentModeConfig();
        const rules = config.specialRules;

        // 时间挑战模式
        if (this.currentMode === 'time-attack') {
            if (this.checkTimeLimit()) {
                game.gameOver();
                return;
            }
        }

        // 生存模式 - 动态障碍物
        if (this.currentMode === 'survival' && rules.dynamicObstacles) {
            if (Math.random() < rules.obstacleSpawnRate && game.obstacles.length < rules.maxObstacles) {
                this.spawnRandomObstacle(game);
            }
        }

        // 竞速模式 - 速度增加
        if (this.currentMode === 'speed-run' && rules.speedIncrease) {
            if (game.gameSpeed > rules.minSpeed) {
                game.gameSpeed = Math.max(rules.minSpeed, game.gameSpeed * rules.speedIncreaseRate);
                game.restartGameLoop();
            }
        }

        // 混沌模式 - 随机事件
        if (this.currentMode === 'chaos' && rules.randomEvents) {
            const now = Date.now();
            if (now - this.lastEventTime > rules.eventInterval * 1000) {
                this.triggerRandomEvent(game);
                this.lastEventTime = now;
            }
        }
    }

    /**
     * 生成随机障碍物
     */
    spawnRandomObstacle(game) {
        const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
        let attempts = 0;

        while (attempts < 50) {
            const x = Math.floor(Math.random() * gridCount);
            const y = Math.floor(Math.random() * gridCount);

            // 检查位置是否安全
            let safe = true;

            // 检查蛇身
            for (const segment of game.snake.body) {
                if (segment.x === x && segment.y === y) {
                    safe = false;
                    break;
                }
            }

            // 检查食物
            if (game.food.position.x === x && game.food.position.y === y) {
                safe = false;
            }

            // 检查现有障碍物
            for (const obstacle of game.obstacles) {
                if (obstacle.x === x && obstacle.y === y) {
                    safe = false;
                    break;
                }
            }

            if (safe) {
                game.obstacles.push(new Obstacle(x, y));
                break;
            }

            attempts++;
        }
    }

    /**
     * 触发随机事件
     */
    triggerRandomEvent(game) {
        const events = [
            'speedBoost',
            'slowDown',
            'multiFood',
            'teleportFood',
            'shrinkSnake',
            'growSnake',
            'clearObstacles',
            'addObstacles'
        ];

        const event = events[Math.floor(Math.random() * events.length)];
        this.executeEvent(event, game);
    }

    /**
     * 执行事件
     */
    executeEvent(eventName, game) {
        switch (eventName) {
            case 'speedBoost':
                game.gameSpeed = Math.max(30, game.gameSpeed * 0.7);
                game.restartGameLoop();
                game.showMessage('⚡ 速度提升！', 2000);
                break;

            case 'slowDown':
                game.gameSpeed = Math.min(200, game.gameSpeed * 1.5);
                game.restartGameLoop();
                game.showMessage('🐌 速度降低！', 2000);
                break;

            case 'multiFood':
                // 生成额外食物
                for (let i = 0; i < 3; i++) {
                    const extraFood = new Food();
                    extraFood.generateNewFood(game.snake, game.obstacles);
                    // 这里需要修改游戏逻辑来支持多个食物
                }
                game.showMessage('🍎 多重食物！', 2000);
                break;

            case 'shrinkSnake':
                if (game.snake.body.length > 3) {
                    game.snake.body.pop();
                    game.showMessage('📉 蛇身缩短！', 2000);
                }
                break;

            case 'growSnake':
                game.snake.grow();
                game.showMessage('📈 蛇身增长！', 2000);
                break;

            case 'clearObstacles':
                game.obstacles = [];
                game.showMessage('🧹 障碍物清除！', 2000);
                break;

            case 'addObstacles':
                for (let i = 0; i < 3; i++) {
                    this.spawnRandomObstacle(game);
                }
                game.showMessage('🧱 新增障碍物！', 2000);
                break;
        }
    }

    /**
     * 应用模式修饰符
     */
    applyModeModifiers(game) {
        const config = this.getCurrentModeConfig();
        const rules = config.specialRules;

        // 禅意模式 - 无边界
        if (this.currentMode === 'zen' && rules.noWalls) {
            game.snake.wallPassThrough = true;
        }

        // 禅意模式 - 慢速度
        if (this.currentMode === 'zen' && rules.slowSpeed) {
            game.gameSpeed = 200;
        }
    }

    /**
     * 计算分数倍数
     */
    getScoreMultiplier() {
        return this.getCurrentModeConfig().scoreMultiplier;
    }

    /**
     * 检查是否应该死亡
     */
    shouldDie(game) {
        const config = this.getCurrentModeConfig();

        // 禅意模式不死亡
        if (this.currentMode === 'zen' && config.specialRules.noDeath) {
            return false;
        }

        return true;
    }
}

/**
 * 主题管理器 - 管理游戏主题切换
 */
class ThemeManager {
    constructor() {
        this.currentTheme = 'neon';
        this.loadTheme();
    }

    /**
     * 设置主题
     */
    setTheme(themeName) {
        if (!THEMES[themeName]) {
            console.warn('主题不存在:', themeName);
            return;
        }

        this.currentTheme = themeName;
        document.documentElement.setAttribute('data-theme', themeName);
        localStorage.setItem('gameTheme', themeName);

        console.log('主题已切换到:', THEMES[themeName].name);
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return THEMES[this.currentTheme];
    }

    /**
     * 获取当前主题颜色
     */
    getColors() {
        return this.getCurrentTheme().colors;
    }

    /**
     * 获取当前主题效果设置
     */
    getEffects() {
        return this.getCurrentTheme().effects;
    }

    /**
     * 加载保存的主题
     */
    loadTheme() {
        const savedTheme = localStorage.getItem('gameTheme');
        if (savedTheme && THEMES[savedTheme]) {
            this.setTheme(savedTheme);
        } else {
            this.setTheme(this.currentTheme);
        }
    }

    /**
     * 获取所有可用主题
     */
    getAvailableThemes() {
        return Object.keys(THEMES).map(key => ({
            key,
            name: THEMES[key].name
        }));
    }
}

/**
 * 蛇类 - 管理蛇的状态和行为
 */
class Snake {
    constructor(x, y) {
        this.body = [];
        this.direction = GAME_CONFIG.DIRECTIONS.RIGHT;
        this.nextDirection = GAME_CONFIG.DIRECTIONS.RIGHT;
        this.canTurnAround = true;
        this.wallPassThrough = false;
        this.wallPassThroughTime = 0;
        
        // 初始化蛇身
        for (let i = 0; i < GAME_CONFIG.INITIAL_SNAKE_LENGTH; i++) {
            this.body.push({ x: x - i, y: y });
        }
    }

    /**
     * 更新蛇的方向
     */
    updateDirection() {
        // 防止蛇直接掉头
        if (this.canTurnAround) {
            this.direction = this.nextDirection;
        }
    }

    /**
     * 设置下一个移动方向
     */
    setDirection(newDirection) {
        const opposite = {
            [JSON.stringify(GAME_CONFIG.DIRECTIONS.UP)]: GAME_CONFIG.DIRECTIONS.DOWN,
            [JSON.stringify(GAME_CONFIG.DIRECTIONS.DOWN)]: GAME_CONFIG.DIRECTIONS.UP,
            [JSON.stringify(GAME_CONFIG.DIRECTIONS.LEFT)]: GAME_CONFIG.DIRECTIONS.RIGHT,
            [JSON.stringify(GAME_CONFIG.DIRECTIONS.RIGHT)]: GAME_CONFIG.DIRECTIONS.LEFT
        };

        const currentDirStr = JSON.stringify(this.direction);
        const newDirStr = JSON.stringify(newDirection);
        
        // 防止蛇直接掉头
        if (this.body.length > 1 && JSON.stringify(opposite[currentDirStr]) === newDirStr) {
            return;
        }
        
        this.nextDirection = newDirection;
    }

    /**
     * 移动蛇
     */
    move() {
        this.updateDirection();
        
        const head = { ...this.body[0] };
        head.x += this.direction.x;
        head.y += this.direction.y;
        
        // 处理穿墙效果
        if (this.wallPassThrough) {
            const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
            if (head.x < 0) head.x = gridCount - 1;
            if (head.x >= gridCount) head.x = 0;
            if (head.y < 0) head.y = gridCount - 1;
            if (head.y >= gridCount) head.y = 0;
            
            this.wallPassThroughTime--;
            if (this.wallPassThroughTime <= 0) {
                this.wallPassThrough = false;
            }
        }
        
        this.body.unshift(head);
    }

    /**
     * 增长蛇身（吃到食物时调用）
     */
    grow() {
        // 不移除尾部，让蛇增长
    }

    /**
     * 移除尾部（正常移动时调用）
     */
    removeTail() {
        this.body.pop();
    }

    /**
     * 获取蛇头位置
     */
    getHead() {
        return this.body[0];
    }

    /**
     * 检查是否撞到自己
     */
    checkSelfCollision() {
        const head = this.getHead();
        for (let i = 1; i < this.body.length; i++) {
            if (head.x === this.body[i].x && head.y === this.body[i].y) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否撞墙
     */
    checkWallCollision() {
        if (this.wallPassThrough) return false;
        
        const head = this.getHead();
        const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
        
        return head.x < 0 || head.x >= gridCount || head.y < 0 || head.y >= gridCount;
    }

    /**
     * 启用穿墙效果
     */
    enableWallPassThrough(duration = 100) {
        this.wallPassThrough = true;
        this.wallPassThroughTime = duration;
    }

    /**
     * 绘制蛇
     */
    draw(ctx, themeManager, particleSystem = null, game = null) {
        const colors = themeManager.getColors();
        const effects = themeManager.getEffects();

        this.body.forEach((segment, index) => {
            const segmentColor = index === 0 ? colors.snakeHead : colors.snake;
            ctx.fillStyle = segmentColor;

            // 添加轨迹效果（仅对蛇头）
            if (index === 0 && effects.trail && particleSystem) {
                const headX = segment.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
                const headY = segment.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
                particleSystem.addTrail(headX, headY, colors.snakeHead, 2);
            }

            // 添加发光效果
            if (effects.glow) {
                ctx.save();
                // 设置阴影/发光效果
                ctx.shadowColor = segmentColor;
                ctx.shadowBlur = index === 0 ? 20 : 15; // 蛇头更亮
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;

                // 蛇头特殊渐变效果
                if (index === 0) {
                    const gradient = ctx.createRadialGradient(
                        segment.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                        segment.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                        0,
                        segment.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                        segment.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2,
                        GAME_CONFIG.GRID_SIZE / 2
                    );
                    gradient.addColorStop(0, colors.snakeHead);
                    gradient.addColorStop(1, colors.snake);
                    ctx.fillStyle = gradient;
                }
            }

            // 护盾效果 - 蓝色光晕
            if (game && game.powerUpSystem.hasEffect('invincible')) {
                ctx.save();
                ctx.shadowColor = '#00BCD4';
                ctx.shadowBlur = 25;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
                ctx.globalAlpha = 0.7;
            }

            ctx.fillRect(
                segment.x * GAME_CONFIG.GRID_SIZE + 1,
                segment.y * GAME_CONFIG.GRID_SIZE + 1,
                GAME_CONFIG.GRID_SIZE - 2,
                GAME_CONFIG.GRID_SIZE - 2
            );

            // 重置阴影效果
            if (effects.glow || (game && game.powerUpSystem.hasEffect('invincible'))) {
                ctx.shadowBlur = 0;
                ctx.restore();
            }
            
            // 穿墙效果的视觉提示
            if (this.wallPassThrough && index === 0) {
                ctx.strokeStyle = colors.specialFood.diamond;
                ctx.lineWidth = 2;
                ctx.strokeRect(
                    segment.x * GAME_CONFIG.GRID_SIZE,
                    segment.y * GAME_CONFIG.GRID_SIZE,
                    GAME_CONFIG.GRID_SIZE,
                    GAME_CONFIG.GRID_SIZE
                );
            }
        });
    }
}

/**
 * 食物类 - 管理食物的生成和属性
 */
class Food {
    constructor() {
        this.position = { x: 0, y: 0 };
        this.type = GAME_CONFIG.FOOD_TYPES.NORMAL;
        this.value = 10;
        this.specialEffect = null;
        this.animationFrame = 0;
        this.generateNewFood();
    }

    /**
     * 生成新食物
     */
    generateNewFood(snake = null, obstacles = []) {
        const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
        let validPosition = false;
        
        while (!validPosition) {
            this.position = {
                x: Math.floor(Math.random() * gridCount),
                y: Math.floor(Math.random() * gridCount)
            };
            
            validPosition = true;
            
            // 检查是否与蛇身重叠
            if (snake) {
                for (const segment of snake.body) {
                    if (segment.x === this.position.x && segment.y === this.position.y) {
                        validPosition = false;
                        break;
                    }
                }
            }
            
            // 检查是否与障碍物重叠
            for (const obstacle of obstacles) {
                if (obstacle.x === this.position.x && obstacle.y === this.position.y) {
                    validPosition = false;
                    break;
                }
            }
        }
        
        // 随机生成特殊食物
        this.generateFoodType();
    }

    /**
     * 生成食物类型
     */
    generateFoodType() {
        const random = Math.random();
        
        if (random < 0.1) { // 10% 概率生成金色食物
            this.type = GAME_CONFIG.FOOD_TYPES.GOLDEN;
            this.value = 50;
            this.specialEffect = 'slowDown';
        } else if (random < 0.15) { // 5% 概率生成钻石食物
            this.type = GAME_CONFIG.FOOD_TYPES.DIAMOND;
            this.value = 30;
            this.specialEffect = 'wallPass';
        } else {
            this.type = GAME_CONFIG.FOOD_TYPES.NORMAL;
            this.value = 10;
            this.specialEffect = null;
        }
    }

    /**
     * 获取食物颜色
     */
    getColor(colors) {
        switch (this.type) {
            case GAME_CONFIG.FOOD_TYPES.GOLDEN:
                return colors.specialFood.golden;
            case GAME_CONFIG.FOOD_TYPES.DIAMOND:
                return colors.specialFood.diamond;
            default:
                return colors.food;
        }
    }

    /**
     * 绘制食物
     */
    draw(ctx, themeManager) {
        const colors = themeManager.getColors();
        const effects = themeManager.getEffects();
        this.animationFrame++;
        
        const x = this.position.x * GAME_CONFIG.GRID_SIZE;
        const y = this.position.y * GAME_CONFIG.GRID_SIZE;
        const centerX = x + GAME_CONFIG.GRID_SIZE / 2;
        const centerY = y + GAME_CONFIG.GRID_SIZE / 2;
        
        // 食物的呼吸动画效果
        let pulse = 1;
        if (this.type !== GAME_CONFIG.FOOD_TYPES.NORMAL) {
            pulse = Math.sin(this.animationFrame * 0.2) * 0.15 + 1;
        } else {
            // 普通食物也有轻微的呼吸效果
            pulse = Math.sin(this.animationFrame * 0.1) * 0.05 + 1;
        }

        ctx.save();
        ctx.translate(centerX, centerY);
        ctx.scale(pulse, pulse);
        ctx.translate(-centerX, -centerY);
        
        // 设置绘制样式
        const foodColor = this.getColor(colors);
        ctx.fillStyle = foodColor;

        // 光晕效果
        if (effects.glow) {
            ctx.shadowColor = foodColor;
            // 特殊食物有更强的光晕
            ctx.shadowBlur = this.type !== GAME_CONFIG.FOOD_TYPES.NORMAL ? 20 : 10;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        } else {
            ctx.shadowBlur = 0;
        }

        // 绘制食物
        if (this.type === GAME_CONFIG.FOOD_TYPES.DIAMOND) {
            // 钻石形状
            ctx.beginPath();
            ctx.moveTo(centerX, y + 2);
            ctx.lineTo(x + GAME_CONFIG.GRID_SIZE - 2, centerY);
            ctx.lineTo(centerX, y + GAME_CONFIG.GRID_SIZE - 2);
            ctx.lineTo(x + 2, centerY);
            ctx.closePath();
            ctx.fill();
        } else {
            // 圆形食物
            ctx.beginPath();
            ctx.arc(centerX, centerY, GAME_CONFIG.GRID_SIZE / 2 - 2, 0, 2 * Math.PI);
            ctx.fill();
        }

        // 重置阴影
        ctx.shadowBlur = 0;

        // 恢复变换
        ctx.restore();
    }
}

/**
 * 障碍物类
 */
class Obstacle {
    constructor(x, y) {
        this.x = x;
        this.y = y;
    }

    /**
     * 绘制障碍物
     */
    draw(ctx, themeManager) {
        const colors = themeManager.getColors();
        ctx.fillStyle = colors.obstacle;
        ctx.fillRect(
            this.x * GAME_CONFIG.GRID_SIZE + 1,
            this.y * GAME_CONFIG.GRID_SIZE + 1,
            GAME_CONFIG.GRID_SIZE - 2,
            GAME_CONFIG.GRID_SIZE - 2
        );
        
        // 添加纹理效果
        ctx.fillStyle = 'rgba(139, 69, 19, 0.5)';
        ctx.fillRect(
            this.x * GAME_CONFIG.GRID_SIZE + 3,
            this.y * GAME_CONFIG.GRID_SIZE + 3,
            GAME_CONFIG.GRID_SIZE - 6,
            GAME_CONFIG.GRID_SIZE - 6
        );
    }
}

/**
 * 道具类 - 管理游戏道具
 */
class PowerUp {
    constructor(type, x, y) {
        this.type = type;
        this.position = { x, y };
        this.animationFrame = 0;
        this.spawnTime = Date.now();
        this.duration = this.getTypeDuration();
        this.config = this.getTypeConfig();
    }

    /**
     * 获取道具类型配置
     */
    getTypeConfig() {
        const configs = {
            speed: {
                name: '加速',
                icon: '⚡',
                color: '#FFE135',  // Electric yellow - distinct from theme golds
                effect: 'speed_boost',
                duration: 8000,
                description: '移动速度提升25%'
            },
            slow: {
                name: '减速',
                icon: '🐌',
                color: '#87CEEB',  // Sky blue
                effect: 'speed_slow',
                duration: 6000,
                description: '移动速度降低33%'
            },
            ghost: {
                name: '穿墙',
                icon: '👻',
                color: '#DDA0DD',  // Plum
                effect: 'wall_pass',
                duration: 10000,
                description: '可以穿过墙壁和障碍物'
            },
            bomb: {
                name: '炸弹',
                icon: '💣',
                color: '#FF6347',  // Tomato - distinct from other oranges
                effect: 'clear_obstacles',
                duration: 0,
                description: '清除所有障碍物'
            },
            magnet: {
                name: '磁铁',
                icon: '🧲',
                color: '#E91E63',  // Pink - distinct from other pinks
                effect: 'food_magnet',
                duration: 12000,
                description: '自动吸引附近的食物'
            },
            shield: {
                name: '护盾',
                icon: '🛡️',
                color: '#00BCD4',  // Cyan - distinct from ocean theme
                effect: 'invincible',
                duration: 5000,
                description: '免疫一次碰撞伤害'
            },
            multiply: {
                name: '倍增',
                icon: '✖️',
                color: '#FF9800',  // Orange - distinct from theme golds
                effect: 'score_multiply',
                duration: 15000,
                description: '分数获得翻倍'
            },
            shrink: {
                name: '缩小',
                icon: '📉',
                color: '#4CAF50',  // Green - distinct from snake greens
                effect: 'shrink_snake',
                duration: 0,
                description: '蛇身长度减少2节'
            }
        };

        return configs[this.type] || configs.speed;
    }

    /**
     * 获取道具持续时间
     */
    getTypeDuration() {
        return this.getTypeConfig().duration;
    }

    /**
     * 绘制道具
     */
    draw(ctx, themeManager) {
        this.animationFrame++;

        const x = this.position.x * GAME_CONFIG.GRID_SIZE;
        const y = this.position.y * GAME_CONFIG.GRID_SIZE;
        const centerX = x + GAME_CONFIG.GRID_SIZE / 2;
        const centerY = y + GAME_CONFIG.GRID_SIZE / 2;

        // 旋转和脉冲动画
        const rotation = this.animationFrame * 0.1;
        const pulse = Math.sin(this.animationFrame * 0.2) * 0.2 + 1;

        ctx.save();
        ctx.translate(centerX, centerY);
        ctx.rotate(rotation);
        ctx.scale(pulse, pulse);

        // 绘制背景圆圈
        ctx.fillStyle = this.config.color;
        ctx.globalAlpha = 0.3;
        ctx.beginPath();
        ctx.arc(0, 0, GAME_CONFIG.GRID_SIZE / 2 - 2, 0, Math.PI * 2);
        ctx.fill();

        // 绘制边框
        ctx.globalAlpha = 1;
        ctx.strokeStyle = this.config.color;
        ctx.lineWidth = 2;
        ctx.stroke();

        // 绘制图标
        ctx.font = `${GAME_CONFIG.GRID_SIZE * 0.6}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#ffffff';
        ctx.fillText(this.config.icon, 0, 0);

        ctx.restore();

        // 绘制增强的光晕效果
        if (themeManager.getEffects().glow) {
            ctx.save();

            // 外层光晕
            ctx.globalAlpha = 0.3;
            ctx.shadowColor = this.config.color;
            ctx.shadowBlur = 25;
            ctx.fillStyle = this.config.color;
            ctx.beginPath();
            ctx.arc(centerX, centerY, GAME_CONFIG.GRID_SIZE / 2 + 5, 0, Math.PI * 2);
            ctx.fill();

            // 内层光晕
            ctx.globalAlpha = 0.6;
            ctx.shadowBlur = 15;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 5, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        }
    }

    /**
     * 检查是否过期
     */
    isExpired() {
        // 道具在地图上存在30秒后消失
        return Date.now() - this.spawnTime > 30000;
    }
}

/**
 * 道具系统管理器
 */
class PowerUpSystem {
    constructor() {
        this.powerUps = []; // 地图上的道具
        this.activePowerUps = []; // 激活的道具效果
        this.spawnTimer = 0;
        this.spawnInterval = 15000; // 15秒生成一个道具
        this.lastSpawnTime = 0;
    }

    /**
     * 更新道具系统
     */
    update(game) {
        const now = Date.now();

        // 生成新道具
        if (now - this.lastSpawnTime > this.spawnInterval) {
            this.spawnRandomPowerUp(game);
            this.lastSpawnTime = now;
        }

        // 移除过期的地图道具
        this.powerUps = this.powerUps.filter(powerUp => !powerUp.isExpired());

        // 更新激活的道具效果
        this.updateActivePowerUps(game);
    }

    /**
     * 生成随机道具
     */
    spawnRandomPowerUp(game) {
        // 道具类型和权重配置，确保每个道具概率相等
        const powerUpPool = [
            'speed', 'slow', 'ghost', 'bomb',
            'magnet', 'shield', 'multiply', 'shrink'
        ];

        // 随机选择一个道具类型
        const type = powerUpPool[Math.floor(Math.random() * powerUpPool.length)];

        const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
        let attempts = 0;

        while (attempts < 50) {
            const x = Math.floor(Math.random() * gridCount);
            const y = Math.floor(Math.random() * gridCount);

            // 检查位置是否安全
            if (this.isPositionSafe(x, y, game)) {
                this.powerUps.push(new PowerUp(type, x, y));
                break;
            }

            attempts++;
        }
    }

    /**
     * 检查位置是否安全
     */
    isPositionSafe(x, y, game) {
        // 检查蛇身
        for (const segment of game.snake.body) {
            if (segment.x === x && segment.y === y) {
                return false;
            }
        }

        // 检查食物
        if (game.food.position.x === x && game.food.position.y === y) {
            return false;
        }

        // 检查障碍物
        for (const obstacle of game.obstacles) {
            if (obstacle.x === x && obstacle.y === y) {
                return false;
            }
        }

        // 检查其他道具
        for (const powerUp of this.powerUps) {
            if (powerUp.position.x === x && powerUp.position.y === y) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查道具碰撞
     */
    checkPowerUpCollision(snake) {
        const head = snake.body[0];

        for (let i = this.powerUps.length - 1; i >= 0; i--) {
            const powerUp = this.powerUps[i];
            if (head.x === powerUp.position.x && head.y === powerUp.position.y) {
                // 需要从外部传入game实例，这里先存储powerUp，在handlePowerUpCollected中处理
                this.powerUps.splice(i, 1);
                return powerUp;
            }
        }

        return null;
    }

    /**
     * 激活道具
     */
    activatePowerUp(powerUp, game = null) {
        const activePowerUp = {
            type: powerUp.type,
            config: powerUp.config,
            startTime: Date.now(),
            duration: powerUp.duration
        };

        // 即时效果道具
        if (powerUp.duration === 0) {
            this.applyInstantEffect(powerUp, game);
        } else {
            // 持续效果道具
            this.activePowerUps.push(activePowerUp);
        }

        console.log('道具激活:', powerUp.config.name);
    }

    /**
     * 应用即时效果
     */
    applyInstantEffect(powerUp, game) {
        switch (powerUp.type) {
            case 'bomb':
                if (game) {
                    game.obstacles = [];
                    game.showMessage('💥 障碍物已清除！', 2000);
                }
                break;

            case 'shrink':
                if (game && game.snake.body.length > 3) {
                    game.snake.body.pop();
                    game.snake.body.pop();
                    game.showMessage('📉 蛇身缩短！', 2000);
                }
                break;
        }
    }

    /**
     * 更新激活的道具效果
     */
    updateActivePowerUps(game) {
        const now = Date.now();

        // 应用磁铁效果
        if (this.hasEffect('food_magnet') && game) {
            this.applyMagnetEffect(game);
        }

        this.activePowerUps = this.activePowerUps.filter(powerUp => {
            const elapsed = now - powerUp.startTime;
            return elapsed < powerUp.duration;
        });
    }

    /**
     * 应用磁铁效果 - 吸引食物向蛇移动
     */
    applyMagnetEffect(game) {
        const snakeHead = game.snake.body[0];
        const food = game.food;
        const magnetRange = 8; // 磁铁作用范围（格子数）

        // 计算蛇头和食物之间的距离
        const dx = food.position.x - snakeHead.x;
        const dy = food.position.y - snakeHead.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 如果食物在磁铁范围内
        if (distance <= magnetRange && distance > 1) {
            // 计算移动方向（单位向量）
            const moveX = dx > 0 ? -1 : (dx < 0 ? 1 : 0);
            const moveY = dy > 0 ? -1 : (dy < 0 ? 1 : 0);

            // 计算新位置
            const newX = food.position.x + moveX;
            const newY = food.position.y + moveY;

            // 检查新位置是否有效（不与蛇身重叠，不超出边界）
            const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
            if (newX >= 0 && newX < gridCount && newY >= 0 && newY < gridCount) {
                const isValidPosition = !game.snake.body.some(segment =>
                    segment.x === newX && segment.y === newY
                ) && !game.obstacles.some(obstacle =>
                    obstacle.x === newX && obstacle.y === newY
                );

                if (isValidPosition) {
                    food.position.x = newX;
                    food.position.y = newY;

                    // 添加磁铁吸引的粒子效果
                    if (game.particleSystem) {
                        const foodX = food.position.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
                        const foodY = food.position.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
                        game.particleSystem.addParticle(foodX, foodY, '#E91E63', 1, 0.5);
                    }
                }
            }
        }
    }

    /**
     * 绘制所有道具
     */
    draw(ctx, themeManager) {
        this.powerUps.forEach(powerUp => {
            powerUp.draw(ctx, themeManager);
        });
    }

    /**
     * 获取激活的道具列表
     */
    getActivePowerUps() {
        return this.activePowerUps;
    }

    /**
     * 检查是否有特定效果
     */
    hasEffect(effectType) {
        return this.activePowerUps.some(powerUp => powerUp.config.effect === effectType);
    }

    /**
     * 清除所有道具
     */
    clear() {
        this.powerUps = [];
        this.activePowerUps = [];
    }
}

/**
 * 粒子类 - 管理单个粒子的行为
 */
class Particle {
    constructor(x, y, color, type = 'explosion') {
        this.x = x;
        this.y = y;
        this.color = color;
        this.type = type;
        this.life = 1.0;
        this.maxLife = 1.0;
        this.size = Math.random() * 4 + 2;
        this.alpha = 1.0;

        // 根据类型设置不同的运动参数
        switch (type) {
            case 'explosion':
                this.vx = (Math.random() - 0.5) * 8;
                this.vy = (Math.random() - 0.5) * 8;
                this.decay = 0.02;
                break;
            case 'trail':
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.decay = 0.05;
                this.size = Math.random() * 2 + 1;
                break;
            case 'sparkle':
                this.vx = 0;
                this.vy = 0;
                this.decay = 0.03;
                this.size = Math.random() * 3 + 1;
                break;
        }
    }

    /**
     * 更新粒子状态
     */
    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life -= this.decay;
        this.alpha = this.life / this.maxLife;

        // 添加重力效果（仅对爆炸粒子）
        if (this.type === 'explosion') {
            this.vy += 0.1;
        }

        // 减小粒子大小
        this.size *= 0.98;

        return this.life > 0;
    }

    /**
     * 绘制粒子
     */
    draw(ctx) {
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;

        if (this.type === 'sparkle') {
            // 星形粒子
            ctx.beginPath();
            for (let i = 0; i < 5; i++) {
                const angle = (i * Math.PI * 2) / 5;
                const x = this.x + Math.cos(angle) * this.size;
                const y = this.y + Math.sin(angle) * this.size;
                if (i === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.closePath();
            ctx.fill();
        } else {
            // 圆形粒子
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fill();
        }

        ctx.restore();
    }
}

/**
 * 粒子系统管理器
 */
class ParticleSystem {
    constructor() {
        this.particles = [];
    }

    /**
     * 添加爆炸效果
     */
    addExplosion(x, y, color, count = 15) {
        for (let i = 0; i < count; i++) {
            this.particles.push(new Particle(x, y, color, 'explosion'));
        }
    }

    /**
     * 添加轨迹效果
     */
    addTrail(x, y, color, count = 3) {
        for (let i = 0; i < count; i++) {
            this.particles.push(new Particle(x, y, color, 'trail'));
        }
    }

    /**
     * 添加闪烁效果
     */
    addSparkle(x, y, color, count = 8) {
        for (let i = 0; i < count; i++) {
            const offsetX = (Math.random() - 0.5) * 20;
            const offsetY = (Math.random() - 0.5) * 20;
            this.particles.push(new Particle(x + offsetX, y + offsetY, color, 'sparkle'));
        }
    }

    /**
     * 更新所有粒子
     */
    update() {
        this.particles = this.particles.filter(particle => particle.update());
    }

    /**
     * 绘制所有粒子
     */
    draw(ctx) {
        this.particles.forEach(particle => particle.draw(ctx));
    }

    /**
     * 清除所有粒子
     */
    clear() {
        this.particles = [];
    }

    /**
     * 获取粒子数量
     */
    getParticleCount() {
        return this.particles.length;
    }
}

/**
 * 成就系统管理器
 */
class AchievementSystem {
    constructor() {
        this.achievements = {
            firstFood: {
                id: 'firstFood',
                title: '初次品尝',
                description: '吃到第一个食物',
                icon: '🍎',
                unlocked: false,
                progress: 0,
                target: 1
            },
            speedEater: {
                id: 'speedEater',
                title: '连击高手',
                description: '连续吃到10个食物',
                icon: '⚡',
                unlocked: false,
                progress: 0,
                target: 10
            },
            survivor: {
                id: 'survivor',
                title: '生存专家',
                description: '生存时间达到2分钟',
                icon: '⏰',
                unlocked: false,
                progress: 0,
                target: 120
            },
            highScore: {
                id: 'highScore',
                title: '分数达人',
                description: '单局得分达到500分',
                icon: '🎯',
                unlocked: false,
                progress: 0,
                target: 500
            },
            specialCollector: {
                id: 'specialCollector',
                title: '特殊收集家',
                description: '吃到5个特殊食物',
                icon: '⭐',
                unlocked: false,
                progress: 0,
                target: 5
            },
            perfectGame: {
                id: 'perfectGame',
                title: '完美游戏',
                description: '一局游戏中不撞墙',
                icon: '💎',
                unlocked: false,
                progress: 0,
                target: 1
            },
            longSnake: {
                id: 'longSnake',
                title: '巨蟒',
                description: '蛇身长度达到20节',
                icon: '🐍',
                unlocked: false,
                progress: 0,
                target: 20
            },
            master: {
                id: 'master',
                title: '贪吃蛇大师',
                description: '单局得分达到1000分',
                icon: '👑',
                unlocked: false,
                progress: 0,
                target: 1000
            }
        };

        this.loadAchievements();
    }

    /**
     * 加载保存的成就数据
     */
    loadAchievements() {
        const saved = localStorage.getItem('snakeAchievements');
        if (saved) {
            try {
                const savedAchievements = JSON.parse(saved);
                Object.keys(savedAchievements).forEach(key => {
                    if (this.achievements[key]) {
                        this.achievements[key].unlocked = savedAchievements[key].unlocked;
                        this.achievements[key].progress = savedAchievements[key].progress;
                    }
                });
            } catch (e) {
                console.warn('加载成就数据失败:', e);
            }
        }
    }

    /**
     * 保存成就数据
     */
    saveAchievements() {
        const toSave = {};
        Object.keys(this.achievements).forEach(key => {
            toSave[key] = {
                unlocked: this.achievements[key].unlocked,
                progress: this.achievements[key].progress
            };
        });
        localStorage.setItem('snakeAchievements', JSON.stringify(toSave));
    }

    /**
     * 更新成就进度
     */
    updateProgress(achievementId, value) {
        const achievement = this.achievements[achievementId];
        if (!achievement || achievement.unlocked) return;

        achievement.progress = Math.min(value, achievement.target);

        if (achievement.progress >= achievement.target) {
            this.unlockAchievement(achievementId);
        }

        this.saveAchievements();
    }

    /**
     * 增加成就进度
     */
    addProgress(achievementId, amount = 1) {
        const achievement = this.achievements[achievementId];
        if (!achievement || achievement.unlocked) return;

        this.updateProgress(achievementId, achievement.progress + amount);
    }

    /**
     * 解锁成就
     */
    unlockAchievement(achievementId) {
        const achievement = this.achievements[achievementId];
        if (!achievement || achievement.unlocked) return;

        achievement.unlocked = true;
        achievement.progress = achievement.target;
        this.saveAchievements();

        // 显示成就解锁通知
        this.showAchievementPopup(achievement);

        console.log('成就解锁:', achievement.title);
    }

    /**
     * 显示成就解锁弹窗
     */
    showAchievementPopup(achievement) {
        const popup = document.getElementById('achievement-popup');
        const icon = document.getElementById('achievement-icon');
        const title = document.getElementById('achievement-title');
        const description = document.getElementById('achievement-description');

        icon.textContent = achievement.icon;
        title.textContent = achievement.title;
        description.textContent = achievement.description;

        popup.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            popup.classList.remove('show');
        }, 3000);
    }

    /**
     * 获取所有成就
     */
    getAllAchievements() {
        return Object.values(this.achievements);
    }

    /**
     * 获取已解锁成就数量
     */
    getUnlockedCount() {
        return Object.values(this.achievements).filter(a => a.unlocked).length;
    }

    /**
     * 获取总成就数量
     */
    getTotalCount() {
        return Object.keys(this.achievements).length;
    }

    /**
     * 获取完成度百分比
     */
    getCompletionRate() {
        const unlocked = this.getUnlockedCount();
        const total = this.getTotalCount();
        return Math.round((unlocked / total) * 100);
    }

    /**
     * 重置所有成就
     */
    resetAchievements() {
        Object.values(this.achievements).forEach(achievement => {
            achievement.unlocked = false;
            achievement.progress = 0;
        });
        this.saveAchievements();
    }
}

/**
 * 游戏主类 - 管理整个游戏的状态和逻辑
 */
class Game {
    constructor() {
        console.log('Game构造函数开始');

        this.canvas = document.getElementById('game-canvas');
        if (!this.canvas) {
            console.error('无法找到canvas元素');
            return;
        }
        console.log('Canvas元素获取成功');

        this.ctx = this.canvas.getContext('2d');
        if (!this.ctx) {
            console.error('无法获取canvas上下文');
            return;
        }
        console.log('Canvas上下文获取成功');
        this.snake = null;
        this.food = null;
        this.obstacles = [];
        this.score = 0;
        this.highScore = parseInt(localStorage.getItem('snakeHighScore')) || 0;
        this.gameTime = 0;
        this.foodCount = 0;
        this.gameState = 'waiting'; // waiting, playing, paused, gameOver
        this.difficulty = 'medium';
        this.gameSpeed = GAME_CONFIG.SPEEDS.medium;
        this.gameLoop = null;
        this.timeInterval = null;
        this.settings = {
            sound: true,
            obstacles: false,
            specialFood: true
        };

        // 初始化主题管理器
        this.themeManager = new ThemeManager();
        console.log('主题管理器初始化完成');

        // 初始化粒子系统
        this.particleSystem = new ParticleSystem();
        console.log('粒子系统初始化完成');

        // 初始化成就系统
        this.achievementSystem = new AchievementSystem();
        console.log('成就系统初始化完成');

        // 初始化游戏模式管理器
        this.gameModeManager = new GameModeManager();
        console.log('游戏模式管理器初始化完成');

        // 初始化道具系统
        this.powerUpSystem = new PowerUpSystem();
        console.log('道具系统初始化完成');

        console.log('开始初始化游戏');
        this.initializeGame();

        console.log('设置事件监听器');
        this.setupEventListeners();

        console.log('更新显示');
        this.updateDisplay();

        console.log('显示初始覆盖层');
        this.showOverlay('准备开始游戏', '点击开始按钮开始游戏');

        // 设置主题选择器的初始值
        const themeSelect = document.getElementById('theme');
        if (themeSelect) {
            themeSelect.value = this.themeManager.currentTheme;
        }

        console.log('Game构造函数完成');
    }

    /**
     * 初始化游戏
     */
    initializeGame() {
        const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
        const centerX = Math.floor(gridCount / 2);
        const centerY = Math.floor(gridCount / 2);

        this.snake = new Snake(centerX, centerY);
        this.food = new Food();
        this.food.generateNewFood(this.snake, this.obstacles);

        if (this.settings.obstacles) {
            this.generateObstacles();
        }

        this.score = 0;
        this.gameTime = 0;
        this.foodCount = 0;
        this.gameState = 'waiting';

        this.updateDisplay();
        this.draw();
    }

    /**
     * 生成障碍物
     */
    generateObstacles() {
        this.obstacles = [];
        const gridCount = GAME_CONFIG.CANVAS_SIZE / GAME_CONFIG.GRID_SIZE;
        const obstacleCount = Math.floor(gridCount * gridCount * 0.05); // 5%的格子作为障碍物

        for (let i = 0; i < obstacleCount; i++) {
            let validPosition = false;
            let obstacle;

            while (!validPosition) {
                const x = Math.floor(Math.random() * gridCount);
                const y = Math.floor(Math.random() * gridCount);
                obstacle = new Obstacle(x, y);

                validPosition = true;

                // 检查是否与蛇身重叠
                for (const segment of this.snake.body) {
                    if (segment.x === x && segment.y === y) {
                        validPosition = false;
                        break;
                    }
                }

                // 检查是否与食物重叠
                if (this.food.position.x === x && this.food.position.y === y) {
                    validPosition = false;
                }

                // 检查是否与其他障碍物重叠
                for (const existingObstacle of this.obstacles) {
                    if (existingObstacle.x === x && existingObstacle.y === y) {
                        validPosition = false;
                        break;
                    }
                }
            }

            this.obstacles.push(obstacle);
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (this.gameState !== 'playing') return;

            switch (e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    e.preventDefault();
                    this.snake.setDirection(GAME_CONFIG.DIRECTIONS.UP);
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    e.preventDefault();
                    this.snake.setDirection(GAME_CONFIG.DIRECTIONS.DOWN);
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    e.preventDefault();
                    this.snake.setDirection(GAME_CONFIG.DIRECTIONS.LEFT);
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    e.preventDefault();
                    this.snake.setDirection(GAME_CONFIG.DIRECTIONS.RIGHT);
                    break;
                case ' ':
                    e.preventDefault();
                    this.togglePause();
                    break;
            }
        });

        // 移动端触摸控制
        const mobileControls = document.querySelectorAll('.direction-btn');
        mobileControls.forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (this.gameState !== 'playing') return;

                const direction = e.currentTarget.dataset.direction;
                switch (direction) {
                    case 'up':
                        this.snake.setDirection(GAME_CONFIG.DIRECTIONS.UP);
                        break;
                    case 'down':
                        this.snake.setDirection(GAME_CONFIG.DIRECTIONS.DOWN);
                        break;
                    case 'left':
                        this.snake.setDirection(GAME_CONFIG.DIRECTIONS.LEFT);
                        break;
                    case 'right':
                        this.snake.setDirection(GAME_CONFIG.DIRECTIONS.RIGHT);
                        break;
                }
            });
        });

        // 游戏控制按钮
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const restartBtn = document.getElementById('restart-btn');

        if (startBtn) {
            startBtn.addEventListener('click', () => {
                console.log('开始游戏按钮被点击');
                this.startGame();
            });
        } else {
            console.error('找不到开始按钮元素');
        }

        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => this.togglePause());
        } else {
            console.error('找不到暂停按钮元素');
        }

        if (restartBtn) {
            restartBtn.addEventListener('click', () => this.restartGame());
        } else {
            console.error('找不到重新开始按钮元素');
        }

        // 难度选择
        document.getElementById('difficulty').addEventListener('change', (e) => {
            this.difficulty = e.target.value;
            this.gameSpeed = GAME_CONFIG.SPEEDS[this.difficulty];
            if (this.gameState === 'playing') {
                this.restartGameLoop();
            }
        });

        // 主题选择
        document.getElementById('theme').addEventListener('change', (e) => {
            this.themeManager.setTheme(e.target.value);
            // 重新绘制游戏画面以应用新主题
            if (this.gameState === 'waiting' || this.gameState === 'paused') {
                this.draw();
            }
        });

        // 游戏模式选择
        document.getElementById('game-mode').addEventListener('change', (e) => {
            this.gameModeManager.setMode(e.target.value);
            // 重新初始化游戏以应用新模式
            if (this.gameState === 'waiting') {
                this.initializeGame();
                this.draw();
            }
        });

        // 设置选项
        document.getElementById('sound-toggle').addEventListener('change', (e) => {
            this.settings.sound = e.target.checked;
        });

        document.getElementById('obstacles-toggle').addEventListener('change', (e) => {
            this.settings.obstacles = e.target.checked;
            if (this.gameState === 'waiting') {
                this.initializeGame();
            }
        });

        document.getElementById('special-food-toggle').addEventListener('change', (e) => {
            this.settings.specialFood = e.target.checked;
        });

        // 成就按钮
        document.getElementById('achievements-btn').addEventListener('click', () => {
            this.showAchievementsModal();
        });

        // 成就弹窗关闭按钮
        document.getElementById('close-achievement').addEventListener('click', () => {
            document.getElementById('achievement-popup').classList.remove('show');
        });

        // 成就列表关闭按钮
        document.getElementById('close-achievements').addEventListener('click', () => {
            document.getElementById('achievements-modal').classList.remove('show');
        });

        // 玩法说明按钮
        document.getElementById('help-btn').addEventListener('click', () => {
            document.getElementById('help-modal').classList.add('show');
        });

        // 玩法说明关闭按钮
        document.getElementById('close-help').addEventListener('click', () => {
            document.getElementById('help-modal').classList.remove('show');
        });
    }

    /**
     * 开始游戏
     */
    startGame() {
        console.log('startGame方法被调用，当前状态:', this.gameState);

        if (this.gameState === 'waiting' || this.gameState === 'gameOver') {
            console.log('重新初始化游戏');
            this.initializeGame();
        }

        console.log('设置游戏状态为playing');
        this.gameState = 'playing';

        // 应用游戏模式修饰符
        this.gameModeManager.applyModeModifiers(this);

        console.log('启动游戏循环');
        this.startGameLoop();

        console.log('启动时间计数器');
        this.startTimeCounter();

        console.log('更新按钮状态');
        this.updateButtons();

        console.log('隐藏覆盖层');
        this.hideOverlay();

        // 显示模式开始消息
        const modeConfig = this.gameModeManager.getCurrentModeConfig();
        this.showMessage(`${modeConfig.icon} ${modeConfig.name} 开始！`, 2000);

        console.log('游戏启动完成');
    }

    /**
     * 暂停/继续游戏
     */
    togglePause() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            this.stopGameLoop();
            this.stopTimeCounter();
            this.showOverlay('游戏暂停', '点击继续按钮恢复游戏');
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.startGameLoop();
            this.startTimeCounter();
            this.hideOverlay();
        }
        this.updateButtons();
    }

    /**
     * 重新开始游戏
     */
    restartGame() {
        this.stopGameLoop();
        this.stopTimeCounter();
        this.initializeGame();
        this.updateButtons();
        this.showOverlay('准备开始游戏', '点击开始按钮开始游戏');
    }

    /**
     * 游戏结束
     */
    gameOver() {
        this.gameState = 'gameOver';
        this.stopGameLoop();
        this.stopTimeCounter();

        // 更新最高分
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('snakeHighScore', this.highScore.toString());
        }

        // 播放游戏结束音效
        this.playSound('game-over');

        // 显示游戏结束界面
        this.showGameOverScreen();
        this.updateButtons();
        this.updateDisplay();
    }

    /**
     * 游戏主循环
     */
    gameLoopMethod() {
        if (this.gameState !== 'playing') return;

        // 移动蛇
        this.snake.move();

        // 检查碰撞（某些模式可能不死亡，护盾可以免疫）
        if (this.checkCollisions() && this.gameModeManager.shouldDie(this)) {
            // 检查护盾效果
            if (this.powerUpSystem.hasEffect('invincible')) {
                // 消耗护盾效果
                this.powerUpSystem.activePowerUps = this.powerUpSystem.activePowerUps.filter(
                    p => p.config.effect !== 'invincible'
                );
                this.showMessage('🛡️ 护盾保护！', 2000);
            } else {
                this.gameOver();
                return;
            }
        }

        // 检查是否吃到食物
        if (this.checkFoodCollision()) {
            this.handleFoodEaten();
        } else {
            this.snake.removeTail();
        }

        // 检查道具碰撞
        const collectedPowerUp = this.powerUpSystem.checkPowerUpCollision(this.snake);
        if (collectedPowerUp) {
            this.handlePowerUpCollected(collectedPowerUp);
        }

        // 更新粒子系统
        this.particleSystem.update();

        // 更新道具系统
        this.powerUpSystem.update(this);

        // 更新游戏模式特殊逻辑
        this.gameModeManager.update(this);

        // 更新成就（定期检查）
        this.updateAchievements();

        // 重绘游戏
        this.draw();
        this.updateDisplay();
    }

    /**
     * 检查碰撞
     */
    checkCollisions() {
        // 检查撞墙
        if (this.snake.checkWallCollision()) {
            return true;
        }

        // 检查撞自己
        if (this.snake.checkSelfCollision()) {
            return true;
        }

        // 检查撞障碍物
        if (this.settings.obstacles) {
            const head = this.snake.getHead();
            for (const obstacle of this.obstacles) {
                if (head.x === obstacle.x && head.y === obstacle.y) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查是否吃到食物
     */
    checkFoodCollision() {
        const head = this.snake.getHead();
        return head.x === this.food.position.x && head.y === this.food.position.y;
    }

    /**
     * 处理吃到食物的逻辑
     */
    handleFoodEaten() {
        const colors = this.themeManager.getColors();
        const effects = this.themeManager.getEffects();

        // 获取食物位置用于粒子效果
        const foodX = this.food.position.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
        const foodY = this.food.position.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
        const foodColor = this.food.getColor(colors);

        // 增加分数（应用模式倍数和道具倍数）
        const baseScore = this.food.value;
        let multiplier = this.gameModeManager.getScoreMultiplier();

        // 道具倍数效果
        if (this.powerUpSystem.hasEffect('score_multiply')) {
            multiplier *= 2;
        }

        this.score += Math.round(baseScore * multiplier);
        this.foodCount++;

        // 添加粒子效果
        if (effects.particles) {
            if (this.food.type === GAME_CONFIG.FOOD_TYPES.NORMAL) {
                this.particleSystem.addExplosion(foodX, foodY, foodColor, 10);
            } else {
                // 特殊食物更华丽的效果
                this.particleSystem.addExplosion(foodX, foodY, foodColor, 15);
                this.particleSystem.addSparkle(foodX, foodY, foodColor, 8);
            }
        }

        // 处理特殊食物效果
        if (this.settings.specialFood && this.food.specialEffect) {
            this.handleSpecialFoodEffect(this.food.specialEffect);
            this.handleSpecialFoodAchievement();
            this.playSound('special');
        } else {
            this.playSound('eat');
        }

        // 蛇增长
        this.snake.grow();

        // 更新成就
        this.updateAchievements();

        // 生成新食物
        this.food.generateNewFood(this.snake, this.obstacles);

        // 如果不启用特殊食物，强制生成普通食物
        if (!this.settings.specialFood) {
            this.food.type = GAME_CONFIG.FOOD_TYPES.NORMAL;
            this.food.value = 10;
            this.food.specialEffect = null;
        }
    }

    /**
     * 处理特殊食物效果
     */
    handleSpecialFoodEffect(effect) {
        switch (effect) {
            case 'slowDown':
                // 临时减速
                const originalSpeed = this.gameSpeed;
                this.gameSpeed = Math.min(this.gameSpeed + 50, 200);
                this.restartGameLoop();

                setTimeout(() => {
                    this.gameSpeed = originalSpeed;
                    if (this.gameState === 'playing') {
                        this.restartGameLoop();
                    }
                }, 5000); // 5秒后恢复
                break;

            case 'wallPass':
                // 启用穿墙效果
                this.snake.enableWallPassThrough(50); // 50帧，约5秒
                break;
        }
    }

    /**
     * 启动游戏循环
     */
    startGameLoop() {
        this.stopGameLoop();
        this.gameLoop = setInterval(() => this.gameLoopMethod(), this.gameSpeed);
    }

    /**
     * 停止游戏循环
     */
    stopGameLoop() {
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
            this.gameLoop = null;
        }
    }

    /**
     * 重启游戏循环（用于改变速度）
     */
    restartGameLoop() {
        if (this.gameState === 'playing') {
            this.startGameLoop();
        }
    }

    /**
     * 启动时间计数器
     */
    startTimeCounter() {
        this.stopTimeCounter();
        this.timeInterval = setInterval(() => {
            this.gameTime++;
            this.updateDisplay();
        }, 1000);
    }

    /**
     * 停止时间计数器
     */
    stopTimeCounter() {
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
            this.timeInterval = null;
        }
    }

    /**
     * 绘制游戏画面
     */
    draw() {
        const colors = this.themeManager.getColors();

        // 清空画布
        this.ctx.fillStyle = colors.background;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格（可选）
        this.drawGrid();

        // 绘制障碍物
        if (this.settings.obstacles) {
            this.obstacles.forEach(obstacle => obstacle.draw(this.ctx, this.themeManager));
        }

        // 绘制食物
        this.food.draw(this.ctx, this.themeManager);

        // 绘制道具
        this.powerUpSystem.draw(this.ctx, this.themeManager);

        // 绘制蛇
        this.snake.draw(this.ctx, this.themeManager, this.particleSystem, this);

        // 绘制磁铁效果范围
        if (this.powerUpSystem.hasEffect('food_magnet')) {
            this.drawMagnetRange();
        }

        // 绘制激活的道具指示器
        this.drawActivePowerUps();

        // 绘制粒子效果
        this.particleSystem.draw(this.ctx);
    }

    /**
     * 绘制网格
     */
    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        for (let i = 0; i <= GAME_CONFIG.CANVAS_SIZE; i += GAME_CONFIG.GRID_SIZE) {
            this.ctx.beginPath();
            this.ctx.moveTo(i, 0);
            this.ctx.lineTo(i, GAME_CONFIG.CANVAS_SIZE);
            this.ctx.stroke();

            this.ctx.beginPath();
            this.ctx.moveTo(0, i);
            this.ctx.lineTo(GAME_CONFIG.CANVAS_SIZE, i);
            this.ctx.stroke();
        }
    }

    /**
     * 绘制磁铁效果范围
     */
    drawMagnetRange() {
        const snakeHead = this.snake.body[0];
        const centerX = snakeHead.x * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
        const centerY = snakeHead.y * GAME_CONFIG.GRID_SIZE + GAME_CONFIG.GRID_SIZE / 2;
        const magnetRange = 8 * GAME_CONFIG.GRID_SIZE; // 8格的范围

        this.ctx.save();

        // 绘制磁铁范围圆圈
        this.ctx.strokeStyle = '#E91E63';
        this.ctx.lineWidth = 2;
        this.ctx.globalAlpha = 0.3;
        this.ctx.setLineDash([5, 5]); // 虚线效果

        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, magnetRange, 0, Math.PI * 2);
        this.ctx.stroke();

        // 绘制磁铁图标
        this.ctx.globalAlpha = 0.8;
        this.ctx.font = '16px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillStyle = '#E91E63';
        this.ctx.fillText('🧲', centerX, centerY - magnetRange - 20);

        this.ctx.restore();
    }

    /**
     * 绘制激活的道具指示器
     */
    drawActivePowerUps() {
        const activePowerUps = this.powerUpSystem.getActivePowerUps();
        if (activePowerUps.length === 0) return;

        this.ctx.save();

        // 在右上角显示激活的道具
        const startX = GAME_CONFIG.CANVAS_SIZE - 60;
        const startY = 20;
        const iconSize = 30;
        const spacing = 35;

        activePowerUps.forEach((powerUp, index) => {
            const x = startX;
            const y = startY + index * spacing;

            // 计算剩余时间百分比
            const elapsed = Date.now() - powerUp.startTime;
            const remaining = Math.max(0, powerUp.duration - elapsed);
            const progress = remaining / powerUp.duration;

            // 绘制背景圆圈
            this.ctx.fillStyle = powerUp.config.color;
            this.ctx.globalAlpha = 0.3;
            this.ctx.beginPath();
            this.ctx.arc(x, y, iconSize / 2, 0, Math.PI * 2);
            this.ctx.fill();

            // 绘制进度圆弧
            this.ctx.globalAlpha = 0.8;
            this.ctx.strokeStyle = powerUp.config.color;
            this.ctx.lineWidth = 3;
            this.ctx.beginPath();
            this.ctx.arc(x, y, iconSize / 2 - 2, -Math.PI / 2, -Math.PI / 2 + (progress * Math.PI * 2));
            this.ctx.stroke();

            // 绘制图标
            this.ctx.globalAlpha = 1;
            this.ctx.font = '16px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillStyle = '#ffffff';
            this.ctx.fillText(powerUp.config.icon, x, y);

            // 绘制剩余时间
            this.ctx.font = '10px Arial';
            this.ctx.fillStyle = powerUp.config.color;
            this.ctx.fillText(Math.ceil(remaining / 1000) + 's', x, y + iconSize / 2 + 12);
        });

        this.ctx.restore();
    }

    /**
     * 播放音效
     */
    playSound(soundName) {
        if (!this.settings.sound) return;

        const audio = document.getElementById(`${soundName}-sound`);
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(e => {
                console.log('音频播放失败:', e);
            });
        }
    }

    /**
     * 显示成就列表模态框
     */
    showAchievementsModal() {
        const modal = document.getElementById('achievements-modal');
        const achievementsList = document.getElementById('achievements-list');
        const unlockedCount = document.getElementById('unlocked-count');
        const totalAchievements = document.getElementById('total-achievements');
        const completionRate = document.getElementById('completion-rate');

        // 更新统计信息
        unlockedCount.textContent = this.achievementSystem.getUnlockedCount();
        totalAchievements.textContent = this.achievementSystem.getTotalCount();
        completionRate.textContent = this.achievementSystem.getCompletionRate() + '%';

        // 清空并重新生成成就列表
        achievementsList.innerHTML = '';

        this.achievementSystem.getAllAchievements().forEach(achievement => {
            const item = document.createElement('div');
            item.className = `achievement-item ${achievement.unlocked ? 'unlocked' : 'locked'}`;

            const progressPercent = Math.round((achievement.progress / achievement.target) * 100);

            item.innerHTML = `
                <div class="achievement-item-icon">${achievement.icon}</div>
                <div class="achievement-item-info">
                    <h4 class="achievement-item-title">${achievement.title}</h4>
                    <p class="achievement-item-description">${achievement.description}</p>
                </div>
                <div class="achievement-item-progress">
                    <div class="achievement-progress-text">${achievement.progress}/${achievement.target}</div>
                    <div class="achievement-progress-bar">
                        <div class="achievement-progress-fill" style="width: ${progressPercent}%"></div>
                    </div>
                </div>
            `;

            achievementsList.appendChild(item);
        });

        modal.classList.add('show');
    }

    /**
     * 更新成就进度
     */
    updateAchievements() {
        // 第一个食物
        if (this.foodCount >= 1) {
            this.achievementSystem.updateProgress('firstFood', 1);
        }

        // 连击高手 - 连续吃食物
        this.achievementSystem.updateProgress('speedEater', this.foodCount);

        // 生存专家 - 生存时间
        this.achievementSystem.updateProgress('survivor', this.gameTime);

        // 分数达人和大师
        this.achievementSystem.updateProgress('highScore', this.score);
        this.achievementSystem.updateProgress('master', this.score);

        // 巨蟒 - 蛇身长度
        this.achievementSystem.updateProgress('longSnake', this.snake.body.length);
    }

    /**
     * 处理特殊食物成就
     */
    handleSpecialFoodAchievement() {
        this.achievementSystem.addProgress('specialCollector', 1);
    }

    /**
     * 处理道具收集
     */
    handlePowerUpCollected(powerUp) {
        this.showMessage(`${powerUp.config.icon} ${powerUp.config.name}！`, 2000);
        this.playSound('powerup');

        // 激活道具效果
        this.powerUpSystem.activatePowerUp(powerUp, this);

        // 应用道具效果
        this.applyPowerUpEffects();
    }

    /**
     * 应用道具效果
     */
    applyPowerUpEffects() {
        // 重置游戏速度到基础值
        const baseSpeed = GAME_CONFIG.SPEEDS[this.difficulty];
        this.gameSpeed = baseSpeed;

        // 速度效果 - 优化参数避免过度加速/减速
        if (this.powerUpSystem.hasEffect('speed_boost')) {
            // 加速25%而不是50%，避免太快撞墙
            this.gameSpeed = Math.max(50, this.gameSpeed * 0.75);
        } else if (this.powerUpSystem.hasEffect('speed_slow')) {
            // 减速33%而不是50%，避免太慢
            this.gameSpeed = Math.min(250, this.gameSpeed * 1.33);
        }

        // 穿墙效果
        this.snake.wallPassThrough = this.powerUpSystem.hasEffect('wall_pass');

        // 重启游戏循环以应用速度变化
        if (this.powerUpSystem.hasEffect('speed_boost') || this.powerUpSystem.hasEffect('speed_slow')) {
            this.restartGameLoop();
        }

        // 其他效果在各自的更新循环中处理：
        // - food_magnet: 在powerUpSystem.updateActivePowerUps中处理
        // - score_multiply: 在handleFoodEaten中处理
        // - invincible: 在checkCollisions中处理
        // - clear_obstacles, shrink_snake: 即时效果，在applyInstantEffect中处理
    }

    /**
     * 显示临时消息
     */
    showMessage(message, duration = 3000) {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = 'game-message';
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: var(--primary-color);
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 1000;
            animation: messageSlide 0.5s ease;
            pointer-events: none;
        `;

        // 添加到游戏容器
        this.canvas.parentElement.appendChild(messageEl);

        // 自动移除
        setTimeout(() => {
            if (messageEl.parentElement) {
                messageEl.parentElement.removeChild(messageEl);
            }
        }, duration);
    }

    /**
     * 更新显示信息
     */
    updateDisplay() {
        document.getElementById('current-score').textContent = this.score;
        document.getElementById('high-score').textContent = this.highScore;

        // 时间挑战模式显示剩余时间
        if (this.gameModeManager.currentMode === 'time-attack') {
            const remaining = this.gameModeManager.getRemainingTime();
            if (remaining !== null) {
                document.getElementById('game-time').textContent = `剩余: ${this.formatTime(Math.floor(remaining))}`;
            }
        } else {
            document.getElementById('game-time').textContent = this.formatTime(this.gameTime);
        }

        // 更新激活的道具显示
        const activePowerUps = this.powerUpSystem.getActivePowerUps();
        const powerUpDisplay = document.getElementById('active-powerups');
        if (activePowerUps.length > 0) {
            const powerUpText = activePowerUps.map(p => p.config.icon).join(' ');
            powerUpDisplay.textContent = powerUpText;
        } else {
            powerUpDisplay.textContent = '无';
        }
    }

    /**
     * 格式化时间显示
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    /**
     * 更新按钮状态
     */
    updateButtons() {
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');

        switch (this.gameState) {
            case 'waiting':
            case 'gameOver':
                startBtn.disabled = false;
                pauseBtn.disabled = true;
                startBtn.innerHTML = '<span class="btn-icon">▶</span>开始游戏';
                pauseBtn.innerHTML = '<span class="btn-icon">⏸</span>暂停';
                break;
            case 'playing':
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<span class="btn-icon">⏸</span>暂停';
                break;
            case 'paused':
                startBtn.disabled = true;
                pauseBtn.disabled = false;
                pauseBtn.innerHTML = '<span class="btn-icon">▶</span>继续';
                break;
        }
    }

    /**
     * 显示覆盖层
     */
    showOverlay(title, message) {
        const overlay = document.getElementById('game-overlay');
        const overlayTitle = document.getElementById('overlay-title');
        const overlayMessage = document.getElementById('overlay-message');
        const overlayStats = document.getElementById('overlay-stats');

        overlayTitle.textContent = title;
        overlayMessage.textContent = message;
        overlayStats.style.display = 'none';

        overlay.classList.remove('fade-out');
        overlay.classList.add('fade-in');
        overlay.style.display = 'flex';
    }

    /**
     * 隐藏覆盖层
     */
    hideOverlay() {
        const overlay = document.getElementById('game-overlay');
        overlay.classList.remove('fade-in');
        overlay.classList.add('fade-out');

        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
    }

    /**
     * 显示游戏结束界面
     */
    showGameOverScreen() {
        const overlay = document.getElementById('game-overlay');
        const overlayTitle = document.getElementById('overlay-title');
        const overlayMessage = document.getElementById('overlay-message');
        const overlayStats = document.getElementById('overlay-stats');

        overlayTitle.textContent = '游戏结束';
        overlayMessage.textContent = this.score > this.highScore ? '恭喜！创造了新纪录！' : '再接再厉！';

        // 显示统计信息
        document.getElementById('final-score').textContent = this.score;
        document.getElementById('final-time').textContent = this.formatTime(this.gameTime);
        document.getElementById('food-count').textContent = this.foodCount;
        overlayStats.style.display = 'flex';

        overlay.classList.remove('fade-out');
        overlay.classList.add('fade-in');
        overlay.style.display = 'flex';
    }
}

/**
 * 音频管理器
 */
class AudioManager {
    constructor() {
        this.sounds = {};
        this.loadSounds();
    }

    /**
     * 加载音效文件
     */
    loadSounds() {
        // 由于我们使用的是简单的HTML音频元素，这里主要是预加载
        const soundFiles = ['eat', 'game-over', 'special'];

        soundFiles.forEach(soundName => {
            const audio = document.getElementById(`${soundName}-sound`);
            if (audio) {
                this.sounds[soundName] = audio;
                audio.load();
            }
        });
    }
}

/**
 * 工具函数
 */
const Utils = {
    /**
     * 检测是否为移动设备
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 获取随机颜色
     */
    getRandomColor() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
        return colors[Math.floor(Math.random() * colors.length)];
    },

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// 游戏实例
let game;

/**
 * 页面加载完成后初始化游戏
 */
document.addEventListener('DOMContentLoaded', () => {
    // 检测移动设备并显示相应控制器
    if (Utils.isMobile()) {
        document.getElementById('mobile-controls').style.display = 'block';
    }

    // 初始化音频管理器
    const audioManager = new AudioManager();

    // 初始化游戏
    game = new Game();

    // 添加窗口大小改变事件监听
    window.addEventListener('resize', Utils.debounce(() => {
        // 响应式调整
        if (window.innerWidth <= 768) {
            document.getElementById('mobile-controls').style.display = 'block';
        } else if (!Utils.isMobile()) {
            document.getElementById('mobile-controls').style.display = 'none';
        }
    }, 250));

    console.log('贪吃蛇游戏已加载完成！');
});
