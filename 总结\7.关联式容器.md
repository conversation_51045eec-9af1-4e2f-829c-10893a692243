# 关联式容器 学习笔记

## 1. 主要知识点概括

### 1.1 关联式容器概述
- **定义**：STL中根据键值对元素进行自动排序和快速查找的容器
- **主要容器**：`set`（集合）和`map`（映射）
- **底层实现**：红黑树（平衡二叉搜索树）
- **头文件**：`<set>`和`<map>`
- **特点**：自动排序、快速查找、元素唯一性

### 1.2 pair类型
- **定义**：能够存储两种不同类型变量的类模板
- **头文件**：`<utility>`
- **作用**：作为map容器的元素类型（键值对）
- **访问方式**：通过`first`和`second`成员访问

## 2. 重要概念辨析

### 2.1 set vs map 对比

| 特性         | set                      | map                  |
| ------------ | ------------------------ | -------------------- |
| **存储内容** | **单一元素**             | **键值对（pair）**   |
| **元素类型** | **基本类型或自定义类型** | **pair<Key, Value>** |
| **排序依据** | **元素本身**             | **键（key）**        |
| **下标访问** | **不支持**               | **支持**             |
| **主要用途** | **去重、排序、查找**     | **键值映射、字典**   |

### 2.2 关联式容器的共同特征
1. **元素唯一性**：不允许重复元素（set不允许重复值，map不允许重复键）
2. **自动排序**：默认按升序排列
3. **快速查找**：基于红黑树，查找时间复杂度O(log n)
4. **不支持随机访问**：不能通过下标直接访问（map例外）

### 2.3 pair的三种创建方式

| 创建方式          | 语法示例                       | 特点             |
| ----------------- | ------------------------------ | ---------------- |
| **大括号初始化**  | `{1, "hello"}`                 | **最简洁**       |
| **pair构造函数**  | `pair<int,string>(4, "hubei")` | **显式类型**     |
| **make_pair函数** | `make_pair(9, "shenzhen")`     | **自动类型推导** |

## 3. 关键代码示例说明

### 3.1 set的基本操作

#### 构造和遍历
````cpp path=7.关联式容器.md mode=EXCERPT
#include <set>
#include <iostream>
using namespace std;

void test_set() {
    // 四种构造方式
    set<int> number1;                    // 无参构造
    set<int> number2 = {1,3,9,8,9};     // 初始化列表（重复元素9会被去除）
    set<int> number3(number2);          // 拷贝构造
    set<int> number4(number2.begin(), number2.end()); // 迭代器构造
    
    // 遍历set
    for(auto it = number2.begin(); it != number2.end(); ++it) {
        cout << *it << " ";  // 输出：1 3 8 9（自动排序，去重）
    }
}
````

**说明**：
- set自动去除重复元素（9只保留一个）
- 自动按升序排列（1 3 8 9）
- 支持多种构造方式

#### 查找操作
````cpp path=7.关联式容器.md mode=EXCERPT
void test_set_find() {
    set<int> numbers = {1, 3, 5, 7, 9};
    
    // count函数：返回0或1
    if(numbers.count(5)) {
        cout << "找到元素5" << endl;
    }
    
    // find函数：返回迭代器
    auto it = numbers.find(7);
    if(it != numbers.end()) {
        cout << "找到元素：" << *it << endl;
    } else {
        cout << "未找到元素" << endl;
    }
}
````

**说明**：
- `count()`：找到返回1，未找到返回0
- `find()`：找到返回对应迭代器，未找到返回`end()`

#### 插入操作
````cpp path=7.关联式容器.md mode=EXCERPT
void test_set_insert() {
    set<int> numbers = {1, 3, 5};
    
    // 插入单个元素，返回pair<iterator, bool>
    pair<set<int>::iterator, bool> ret = numbers.insert(8);
    if(ret.second) {
        cout << "插入成功：" << *(ret.first) << endl;
    } else {
        cout << "插入失败，元素已存在" << endl;
    }
    
    // 插入多个元素
    int arr[] = {18, 41, 35, 2, 99};
    numbers.insert(arr, arr + 5);  // 迭代器范围插入
    numbers.insert({10, 20, 30});  // 初始化列表插入
}
````

**说明**：
- 插入返回`pair<iterator, bool>`
- `second`为true表示插入成功，false表示元素已存在
- `first`指向插入位置的迭代器

### 3.2 map的基本操作

#### 构造和遍历
````cpp path=7.关联式容器.md mode=EXCERPT
#include <map>
#include <string>
using namespace std;

void test_map() {
    // map构造（多种pair创建方式混合）
    map<int, string> cities = {
        {1, "hello"},                           // 大括号
        {2, "world"},
        pair<int,string>(4, "hubei"),          // pair构造函数
        make_pair(9, "shenzhen"),              // make_pair函数
        make_pair(3, "beijing"),               // 重复key，会被舍弃
        {3, "shanghai"}                        // 重复key，会被舍弃
    };
    
    // 迭代器遍历
    for(auto it = cities.begin(); it != cities.end(); ++it) {
        cout << it->first << ":" << it->second << endl;
        // 或者：cout << (*it).first << ":" << (*it).second << endl;
    }
    
    // 范围for循环遍历
    for(auto& city : cities) {
        cout << city.first << " " << city.second << endl;
    }
}
````

**说明**：
- map元素是`pair<key, value>`类型
- 重复的key会被舍弃（key=3只保留第一个）
- 按key值自动升序排列

#### 查找操作
````cpp path=7.关联式容器.md mode=EXCERPT
void test_map_find() {
    map<int, string> cities = {{1, "beijing"}, {2, "shanghai"}, {3, "guangzhou"}};
    
    // count函数
    if(cities.count(2)) {
        cout << "找到key=2的元素" << endl;
    }
    
    // find函数
    auto it = cities.find(3);
    if(it != cities.end()) {
        cout << "找到：" << it->first << ":" << it->second << endl;
    }
}
````

#### 插入操作
````cpp path=7.关联式容器.md mode=EXCERPT
void test_map_insert() {
    map<int, string> cities = {{1, "beijing"}, {2, "shanghai"}};
    
    // 插入单个元素
    pair<map<int,string>::iterator, bool> ret = 
        cities.insert(pair<int,string>(7, "nanjing"));
    
    if(ret.second) {
        cout << "插入成功：" << ret.first->first 
             << ":" << ret.first->second << endl;
    }
    
    // 插入多个元素
    map<int, string> other = {{10, "tianjin"}, {11, "chongqing"}};
    cities.insert(other.begin(), other.end());  // 迭代器范围
    cities.insert({{12, "hangzhou"}, {13, "suzhou"}});  // 初始化列表
}
````

### 3.3 map的下标操作（重点）⭐⭐⭐

````cpp path=7.关联式容器.md mode=EXCERPT
void test_map_subscript() {
    map<int, string> cities = {{1, "beijing"}, {2, "shanghai"}};
    
    // 读取存在的元素
    cout << cities[1] << endl;  // 输出：beijing
    
    // 修改存在的元素
    cities[2] = "SHANGHAI";     // 修改value
    
    // 访问不存在的key会自动插入
    cout << cities[5] << endl;  // 输出空字符串，同时插入{5, ""}
    
    // 利用下标操作插入新元素
    cities[6] = "shenzhen";     // 插入新的键值对
    
    // 遍历查看结果
    for(auto& city : cities) {
        cout << city.first << ":" << city.second << endl;
    }
}
````

**map下标操作的特点**：
1. **返回value的引用**：可以读取和修改
2. **key不存在时自动插入**：创建默认value
3. **支持写操作**：可以修改value，不影响排序
4. **下标是key值**：不是传统意义的索引

## 4. 学习要点和注意事项

### 4.1 set的重要特性⭐⭐⭐

**优势**：
- **自动去重**：天然的去重容器
- **自动排序**：始终保持有序状态
- **快速查找**：O(log n)时间复杂度

**限制**：
- **不支持下标访问**：没有`operator[]`
- **不能修改元素**：迭代器是const的，保证红黑树结构稳定
- **插入位置不可控**：位置由排序规则决定

### 4.2 map的重要特性⭐⭐⭐

**优势**：
- **键值映射**：天然的字典结构
- **支持下标访问**：通过key访问value
- **自动排序**：按key值排序

**下标操作的注意事项**⚠️：
1. **自动插入行为**：访问不存在的key会插入默认value
2. **性能考虑**：频繁的自动插入可能影响性能
3. **类型要求**：value类型必须有默认构造函数

### 4.3 pair的使用技巧💡

**访问成员**：
- `pair.first`：访问第一个元素
- `pair.second`：访问第二个元素

**创建建议**：
- **简单情况**：使用大括号`{key, value}`
- **复杂类型**：使用`make_pair()`自动推导类型
- **明确类型**：使用`pair<T1, T2>()`构造函数

### 4.4 常见错误和陷阱⚠️

**set常见错误**：
```cpp
set<int> s = {1, 2, 3};
// s[0];  // ❌ 错误：set不支持下标访问
// *s.begin() = 10;  // ❌ 错误：不能修改set元素
```

**map常见陷阱**：
```cpp
map<int, string> m = {{1, "hello"}};
cout << m[5];  // ⚠️ 注意：会自动插入{5, ""}
if(m.count(5)) { /* 现在会返回true */ }
```

**性能陷阱**：
```cpp
// ❌ 低效：每次都可能插入
for(int i = 0; i < 1000; ++i) {
    cout << m[i];  // 如果key不存在，会插入1000个元素
}

// ✅ 高效：先检查再访问
for(int i = 0; i < 1000; ++i) {
    if(m.count(i)) {
        cout << m[i];
    }
}
```

### 4.5 查找操作的选择💡

| 操作        | 返回类型          | 适用场景                     | 性能         |
| ----------- | ----------------- | ---------------------------- | ------------ |
| **count()** | **size_t (0或1)** | **简单的存在性检查**         | **O(log n)** |
| **find()**  | **iterator**      | **需要获取元素进行后续操作** | **O(log n)** |
| **map[]**   | **value引用**     | **确定key存在或允许插入**    | **O(log n)** |

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **关联式容器特征**：自动排序、元素唯一、快速查找
2. **set特点**：存储单一元素，自动去重排序，不支持下标
3. **map特点**：存储键值对，按key排序，支持下标访问
4. **pair类型**：存储两个不同类型的值，通过first/second访问

### ⚠️ 重要规则记忆
1. **元素唯一性**：set不允许重复值，map不允许重复key
2. **自动排序**：默认升序，基于元素值（set）或key值（map）
3. **下标特性**：set不支持，map支持但会自动插入
4. **迭代器限制**：set的迭代器是const的，不能修改元素

### 💡 操作选择指南
```
查找元素：
- 简单检查存在性 → count()
- 需要后续操作 → find()
- map且确定存在 → []

插入元素：
- 单个元素 → insert(element)
- 多个元素 → insert(begin, end) 或 insert({...})
- map新元素 → [] 或 insert()
```

### 🎯 常考知识点
1. **set和map的区别**及使用场景
2. **pair的三种创建方式**
3. **map下标操作的特殊行为**
4. **insert函数的返回值类型**和含义
5. **关联式容器的底层实现**（红黑树）

### 📝 代码模板记忆

**set基本操作**：
````cpp path=7.关联式容器.md mode=EDIT
set<int> s = {1, 3, 5};
s.insert(7);                    // 插入元素
if(s.count(5)) { /*存在*/ }     // 检查存在
auto it = s.find(3);            // 查找元素
if(it != s.end()) { /*找到*/ }
````

**map基本操作**：
````cpp path=7.关联式容器.md mode=EDIT
map<int, string> m = {{1, "hello"}};
m[2] = "world";                 // 插入/修改
if(m.count(1)) { /*存在*/ }     // 检查key存在
cout << m[1];                   // 访问value（可能插入）
````

**pair使用**：
````cpp path=7.关联式容器.md mode=EDIT
pair<int, string> p1 = {1, "hello"};           // 大括号
pair<int, string> p2(2, "world");              // 构造函数
auto p3 = make_pair(3, "test");                 // make_pair
cout << p1.first << ":" << p1.second;          // 访问成员
````

### 🔧 实用技巧
1. **优先使用大括号**：创建pair时最简洁
2. **谨慎使用map[]**：避免意外插入空元素
3. **善用count()**：简单的存在性检查
4. **利用自动排序**：无需手动排序
5. **注意类型要求**：map的value类型需要默认构造函数

### 📚 学习顺序建议
1. 理解关联式容器的基本概念
2. 掌握pair类型的使用
3. 学习set的基本操作
4. 学习map的基本操作和下标特性
5. 理解查找和插入操作的返回值
6. 练习实际应用场景

### 🎓 实际应用场景
- **set**：去重、排序、集合运算
- **map**：字典、缓存、计数器、索引映射
- **pair**：函数返回多个值、临时组合数据

### ⚡ 性能特点
- **时间复杂度**：查找、插入、删除都是O(log n)
- **空间复杂度**：O(n)，红黑树额外开销
- **适用数据量**：中等规模数据（几千到几万）

这份笔记涵盖了关联式容器的核心内容，重点掌握set和map的特性差异、基本操作和使用场景，这些是STL编程的重要基础技能。