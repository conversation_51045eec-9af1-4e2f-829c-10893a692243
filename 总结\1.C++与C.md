# C++与C 学习笔记

## 1. C++基础入门

### 1.1 C++基本信息
- **文件扩展名**：源文件 `.cc/.cpp`，头文件 `.hh/.hpp`
- **编译器安装**：`sudo apt install g++`
- **编译命令**：`g++ 文件名.cc/.cpp [-o name]`

### 1.2 Hello World程序分析
````cpp path=1.C++与C.md mode=EXCERPT
#include <iostream>
using namespace std;

int main(int argc, char * argv[]){
    cout << "hello,world" << endl;
    return 0;
}
````

**关键点说明**：
- `#include <iostream>`：C++标准库头文件（无后缀）
- `using namespace std`：使用标准命名空间
- `cout << ... << endl`：C++输出流语法

## 2. 命名空间（Namespace）

### 2.1 为什么需要命名空间
- **问题**：大型项目中不同开发者可能使用相同的变量/函数名
- **解决方案**：命名空间提供独立的作用域，避免命名冲突

### 2.2 命名空间定义
````cpp path=1.C++与C.md mode=EXCERPT
namespace wd {
    int val1 = 0;
    char val2;
    void func() { /* ... */ }
}
````

### 2.3 三种使用方式

#### 方式一：作用域限定符（::）
````cpp path=1.C++与C.md mode=EXCERPT
void test0(){
    cout << wd::cpp::num << endl;
    wd::cpp::func();
}
````
- **优点**：精确，不会产生冲突
- **缺点**：代码冗长

#### 方式二：using编译指令
````cpp path=1.C++与C.md mode=EXCERPT
void test1(){
    using namespace wd::cpp;
    cout << num << endl;
    func();
}
````

#### 方式三：using声明语句
````cpp path=1.C++与C.md mode=EXCERPT
void test2(){
    using wd::cpp::num;
    using wd::cpp::func;
    cout << num << endl;
    func();
}
````

### 2.4 使用建议 ⚠️
1. 提倡在命名空间中定义变量，避免全局变量
2. **using声明建议用于局部作用域，不要用于全局**
3. 不要在头文件中使用using编译指令（避免命名空间污染）
4. 头文件包含顺序：自定义 → C头文件 → C++头文件 → 第三方库

## 3. const关键字

### 3.1 修饰内置类型

#### 基本用法
````cpp path=1.C++与C.md mode=EXCERPT
const int number1 = 10;
int const number2 = 20;
// const int val; // error: 常量必须初始化
````

#### 面试重点：const常量 vs 宏定义常量
| 特性         | const常量                | 宏定义常量             |
| ------------ | ------------------------ | ---------------------- |
| **发生时机** | 编译时                   | 预处理时（字符串替换） |
| **类型检查** | 有具体类型，执行类型检查 | 无类型，不做类型检查   |
| **安全性**   | 更安全，推荐使用         | 容易出错               |

### 3.2 修饰指针类型 ⭐

#### 三种形式对比
````cpp path=1.C++与C.md mode=EXCERPT
const int * p1;     // 指向常量的指针
int const * p2;     // 指向常量的指针（第二种写法）
int * const p3;     // 常量指针
const int * const p4; // 双重const限定
````

#### 记忆方法
- **const在*左边**：指向常量的指针（不能通过指针改变值，但可以改变指向）
- **const在*右边**：常量指针（不能改变指向，但可以通过指针改变值）

#### 重要规则
- const常量只能用指向常量的指针来指向
- 普通指针不能指向const常量

## 4. new/delete表达式

### 4.1 基本用法
````cpp path=1.C++与C.md mode=EXCERPT
int * p1 = new int;        // 申请空间，不初始化
int * p2 = new int(10);    // 申请空间并初始化为10
delete p1;
delete p2;
````

### 4.2 数组空间申请
````cpp path=1.C++与C.md mode=EXCERPT
int * p3 = new int[10]();     // 申请数组空间并初始化为0
int * p4 = new int[3]{1,2,3}; // 大括号初始化
delete [] p3;
delete [] p4;
````

### 4.3 面试重点：malloc/free vs new/delete
| 特性       | malloc/free                | new/delete       |
| ---------- | -------------------------- | ---------------- |
| **本质**   | 库函数                     | 表达式           |
| **返回值** | void*                      | 相应类型指针     |
| **初始化** | 不初始化（有脏数据）       | 可以直接初始化   |
| **参数**   | 需要指定字节数（`sizeof`） | 自动计算空间大小 |

## 5. 引用（Reference）⭐⭐⭐

### 5.1 引用的概念
- **定义**：引用是已定义变量的别名
- **语法**：`类型 & ref = 变量;`

### 5.2 引用的特点
1. **必须初始化**：声明时必须绑定到变量
2. **不能重新绑定**：一经绑定无法更改
3. **类型必须匹配**：引用类型需与绑定变量类型相同
4. **共享地址**：引用与原变量地址相同

### 5.3 引用的本质
- **底层实现**：引用本质是受限制的指针
- **内存占用**：占据一个指针大小的内存
- **访问限制**：编译器阻止对底层指针的直接访问

### 5.4 引用 vs 指针对比
| 特性         | 引用           | 指针             |
| ------------ | -------------- | ---------------- |
| **初始化**   | 必须初始化     | 可以不初始化     |
| **重新绑定** | 不能修改绑定   | 可以修改指向     |
| **取地址**   | 取到原变量地址 | 取到指针变量地址 |
| **推荐度**   | 更推荐使用     | 灵活但易出错     |

### 5.5 引用作为函数参数 ⭐
````cpp path=1.C++与C.md mode=EXCERPT
void swap3(int & x, int & y){  // 引用传递
    int temp = x;
    x = y;
    y = temp;
}
````

#### 三种参数传递方式
1. **值传递**：发生复制，效率低
2. **指针传递**：不复制，但操作复杂易错
3. **引用传递**：不复制，操作简单，**推荐使用**

#### 常引用参数
````cpp path=1.C++与C.md mode=EXCERPT
void func(const int & x){  // 常引用：不复制且不能修改
    // x = 100; // error
}
````

## 6. 强制转换

### 6.1 C++四种转换操作符

#### static_cast（最常用）
````cpp path=1.C++与C.md mode=EXCERPT
int iNumber = 100;
float fNumber = static_cast<float>(iNumber);
````
- **用途**：正常的类型转换
- **限制**：不能进行任意指针类型间转换

#### const_cast（基本不用）
- **用途**：修改类型的const属性

#### dynamic_cast
- **用途**：基类和派生类间的安全转换

#### reinterpret_cast（慎用）
- **用途**：万能转换，功能强大但危险

## 7. 函数重载（Function Overloading）

### 7.1 基本概念
- **定义**：同一作用域内，函数名相同但参数列表不同的函数组
- **目的**：减少函数名数量，提高代码可读性

### 7.2 重载条件
**可以构成重载**：
- 参数数量不同
- 参数类型不同  
- 参数顺序不同

**不能构成重载**：
- 仅返回类型不同

### 7.3 实现原理：名字改编
- **机制**：编译器根据参数信息改编函数名
- **查看方法**：`g++ -c file.cc` + `nm file.o`

### 7.4 extern "C"
````cpp path=1.C++与C.md mode=EXCERPT
extern "C" void func() {  // 按C方式编译单个函数
}

extern "C" {  // 多个函数按C方式编译
    // ...
}
````

## 8. 默认参数

### 8.1 基本用法
````cpp path=1.C++与C.md mode=EXCERPT
void func(int x, int y = 20, int z = 30);  // 默认参数
````

### 8.2 使用规则
1. **从右到左**：有默认值的参数必须在右侧
2. **声明时给出**：默认参数值在函数声明时指定
3. **不能重复**：声明和定义不能都有默认参数

## 9. 内联函数（inline）

### 9.1 目的和机制
- **目的**：避免函数调用开销，提高执行效率
- **机制**：编译时展开，类似高级的代码替换

### 9.2 使用建议
- **适合**：短小且常用的函数
- **不适合**：包含循环或复杂逻辑的函数

### 9.3 三种函数对比
| 类型         | 优点         | 缺点             |
| ------------ | ------------ | ---------------- |
| **宏函数**   | 无函数开销   | 无类型检查，易错 |
| **普通函数** | 安全，可调试 | 有函数调用开销   |
| **内联函数** | 高效且安全   | 可能导致代码膨胀 |

**结论**：C++中应尽可能用内联函数取代宏函数

## 10. 内存布局（重要概念）

### 10.1 五个内存区域（从高地址到低地址）
1. **栈区**：局部变量，系统控制，高→低增长
2. **堆区**：动态分配，程序员控制，低→高增长  
3. **全局/静态区**：全局变量、静态变量
4. **文字常量区**：字符串常量等
5. **程序代码区**：函数二进制代码

## 📚 复习要点总结

### 🔥 重点掌握
1. **命名空间的三种使用方式**及使用建议
2. **const修饰指针的三种形式**及区别
3. **引用的概念、特点和作为函数参数的优势**
4. **函数重载的条件**和实现原理
5. **内存布局的五个区域**

### ⚠️ 注意事项
1. const常量必须初始化
2. using编译指令建议用于局部作用域
3. 引用必须初始化且不能重新绑定
4. 推荐使用引用而非指针作为函数参数
5. 内联函数适合短小常用的代码

### 💡 面试常考
1. const常量 vs 宏定义常量的区别
2. malloc/free vs new/delete的区别  
3. 引用 vs 指针的联系与区别
4. 函数重载的实现原理
5. 内存布局的分区及各区域特点