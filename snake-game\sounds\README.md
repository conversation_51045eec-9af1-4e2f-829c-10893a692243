# 音频文件说明

由于版权和文件大小限制，这里提供音频文件的说明和获取建议。

## 需要的音频文件

### 1. eat.mp3 / eat.wav
- **用途**: 蛇吃到普通食物时的音效
- **建议**: 短促的"咔嚓"或"叮"声
- **时长**: 0.1-0.3秒
- **格式**: MP3或WAV

### 2. special.mp3 / special.wav
- **用途**: 蛇吃到特殊食物时的音效
- **建议**: 更华丽的音效，如"叮铃"或魔法音效
- **时长**: 0.2-0.5秒
- **格式**: MP3或WAV

### 3. game-over.mp3 / game-over.wav
- **用途**: 游戏结束时的音效
- **建议**: 低沉的"嗡"声或失败音效
- **时长**: 0.5-1.0秒
- **格式**: MP3或WAV

## 音频文件获取建议

1. **免费音效网站**:
   - Freesound.org
   - Zapsplat.com
   - Adobe Audition内置音效库

2. **自制音效**:
   - 使用Audacity等免费软件录制
   - 使用在线音效生成器

3. **游戏开发音效包**:
   - Unity Asset Store
   - GameDev Market

## 文件放置

将音频文件直接放在 `sounds/` 文件夹中，确保文件名与代码中的引用一致：
- `eat.mp3` 或 `eat.wav`
- `special.mp3` 或 `special.wav`
- `game-over.mp3` 或 `game-over.wav`

## 注意事项

- 音频文件应该尽量小，以减少加载时间
- 支持MP3和WAV格式，建议提供两种格式以确保兼容性
- 音量应该适中，避免过于刺耳
- 如果没有音频文件，游戏仍然可以正常运行，只是没有音效
