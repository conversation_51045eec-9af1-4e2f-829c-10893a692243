# 运算符重载 学习笔记

## 1. 主要知识点概括和解释

### 1.1 友元机制
- **定义**：允许其他类/函数访问类的私有成员的机制
- **目的**：在保持封装性的前提下，提供灵活的接口扩展
- **关键字**：`friend`
- **比喻**：将类比作家庭，私有成员是家庭秘密，只有friend才能探听这些秘密

### 1.2 运算符重载
- **定义**：为自定义类型定义运算符的行为，使其与内置类型操作保持一致
- **目的**：提高代码可读性和开发效率，增加代码复用性
- **本质**：定义特殊名称的函数（`operator+`、`operator<<`等）
- **指导思想**：希望自定义类类型在操作时与内置类型保持一致

### 1.3 类型转换
- **隐式转换**：由构造函数实现，其他类型→自定义类型
- **类型转换函数**：由成员函数实现，自定义类型→其他类型

## 2. 重要概念定义和区别

### 2.1 友元的三种形式

| 友元类型         | 语法形式                                  | 使用场景                     | 示例                                     |
| ---------------- | ----------------------------------------- | ---------------------------- | ---------------------------------------- |
| **普通函数友元** | `friend 返回类型 函数名(参数列表);`       | 需要访问私有成员的全局函数   | `friend float distance(Point&, Point&);` |
| **成员函数友元** | `friend 返回类型 类名::函数名(参数列表);` | 一个类的成员函数访问另一个类 | `friend float Line::distance(...);`      |
| **友元类**       | `friend class 类名;`                      | 一个类的所有成员都需要访问   | `friend class Line;`                     |

### 2.2 运算符重载的三种形式

| 重载形式     | 适用场景                 | 参数特点                   | 典型运算符            |
| ------------ | ------------------------ | -------------------------- | --------------------- |
| **友元函数** | **不修改操作数的运算符** | 操作数个数=参数个数        | `+`、`-`、`<<`、`>>`  |
| **普通函数** | **不推荐使用**           | 需要公有get函数，破坏封装  | 一般不使用            |
| **成员函数** | **修改操作数的运算符**   | 左操作数是this，参数个数-1 | `+=`、`++`、`=`、`[]` |

### 2.3 必须用成员函数重载的运算符⭐⭐⭐

| 运算符  | 原因             | 特点                   |
| ------- | ---------------- | ---------------------- |
| **=**   | **赋值语义**     | 必须修改左操作数       |
| **[]**  | **下标访问**     | 需要返回引用以支持修改 |
| **()**  | **函数调用**     | 模拟函数调用行为       |
| **->**  | **成员访问**     | 智能指针的核心功能     |
| **->*** | **成员指针访问** | 高级指针操作           |

### 2.4 前置++与后置++的区别

| 类型       | 函数签名          | 返回类型     | 返回值           | 效率                   |
| ---------- | ----------------- | ------------ | ---------------- | ---------------------- |
| **前置++** | `operator++()`    | **引用类型** | 修改后的对象本身 | 高                     |
| **后置++** | `operator++(int)` | **值类型**   | 修改前的副本     | 低（需要创建临时对象） |

### 2.5 运算符重载规则

| 规则                     | 说明                                   | 示例                |
| ------------------------ | -------------------------------------- | ------------------- |
| **操作数类型限制**       | 至少有一个操作数是自定义类型或枚举类型 | 不能全都是内置类型  |
| **优先级和结合性不变**   | 重载后保持原有的优先级和结合性         | `a == b + c`        |
| **操作数个数不变**       | 不能改变运算符的操作数个数             | `+`永远是二元运算符 |
| **不能有默认参数**       | 设置默认值会改变操作数个数             | 违反上一条规则      |
| **不能创造新运算符**     | 只能重载已存在的运算符                 | 不能创造`@`、`$`等  |
| **逻辑运算符失去短路性** | `&&`和`||`进入函数前计算所有参数       | 不推荐重载          |

### 2.6 不能重载的运算符

| 类型           | 运算符                | 记忆方法           |
| -------------- | --------------------- | ------------------ |
| **带点运算符** | `.`、`.*`、`::`、`?:` | 带点的不能重载     |
| **其他**       | `sizeof`              | 加上sizeof一起记忆 |

## 3. 关键代码示例说明

### 3.1 友元函数示例
````cpp path=5.运算符重载.md mode=EXCERPT
class Point{
public:
    Point(int x, int y) : _ix(x), _iy(y) {}
    
    // 友元函数声明
    friend float distance(const Point & lhs, const Point & rhs);
private:
    int _ix;
    int _iy;
};

// 友元函数定义（可以访问私有成员）
float distance(const Point & lhs, const Point & rhs){
    return sqrt((lhs._ix - rhs._ix)*(lhs._ix - rhs._ix) +
                (lhs._iy - rhs._iy)*(lhs._iy - rhs._iy));
}
````

**说明**：友元函数可以直接访问类的私有成员`_ix`和`_iy`

### 3.2 加法运算符重载（友元形式）
````cpp path=5.运算符重载.md mode=EXCERPT
class Complex{
public:
    Complex(double real, double image) : _real(real), _image(image) {}
    
    // 友元函数声明
    friend Complex operator+(const Complex & lhs, const Complex & rhs);
private:
    double _real;
    double _image;
};

// 运算符重载函数定义
Complex operator+(const Complex & lhs, const Complex & rhs){
    return Complex(lhs._real + rhs._real, lhs._image + rhs._image);
}

// 使用示例
Complex c1(1, 2), c2(3, 4);
Complex c3 = c1 + c2;  // 等价于 operator+(c1, c2)
````

**说明**：
- 友元形式适合不修改操作数的运算符
- 返回新创建的对象，不修改原操作数

### 3.3 +=运算符重载（成员函数形式）
````cpp path=5.运算符重载.md mode=EXCERPT
class Complex{
public:
    // 成员函数形式的+=重载
    Complex& operator+=(const Complex & rhs){
        _real += rhs._real;
        _image += rhs._image;
        return *this;  // 返回自身引用
    }
private:
    double _real;
    double _image;
};

// 使用示例
Complex c1(1, 2), c2(3, 4);
c1 += c2;  // 等价于 c1.operator+=(c2)
````

**说明**：
- 成员函数形式适合修改左操作数的运算符
- 返回`*this`的引用，支持链式操作

### 3.4 前置++和后置++重载
````cpp path=5.运算符重载.md mode=EXCERPT
class Complex{
public:
    // 前置++
    Complex& operator++(){
        ++_real;
        ++_image;
        return *this;
    }
    
    // 后置++（参数int用于区分）
    Complex operator++(int){
        Complex tmp(*this);  // 保存原值
        ++_real;
        ++_image;
        return tmp;  // 返回原值的副本
    }
private:
    double _real;
    double _image;
};
````

**说明**：
- 后置++的`int`参数仅用于区分，不使用
- 后置++需要创建临时对象，效率较低

### 3.5 输出流运算符重载
````cpp path=5.运算符重载.md mode=EXCERPT
class Point {
public:
    Point(int x, int y) : _x(x), _y(y) {}
    
    // 友元函数声明
    friend ostream & operator<<(ostream & os, const Point & rhs);
private:
    int _x;
    int _y;
};

// 友元函数定义
ostream & operator<<(ostream & os, const Point & rhs){
    os << "(" << rhs._x << "," << rhs._y << ")";
    return os;  // 返回流引用，支持链式操作
}

// 使用示例
Point pt(1, 2);
cout << pt << endl;  // 输出：(1,2)
````

**说明**：
- 必须用友元形式（左操作数是流对象）
- 返回流引用支持链式操作

### 3.6 下标运算符重载
````cpp path=5.运算符重载.md mode=EXCERPT
class CharArray {
public:
    // const版本（只读）
    const char & operator[](size_t index) const {
        return _data[index];
    }
    
    // 非const版本（可读写）
    char & operator[](size_t index) {
        return _data[index];
    }
private:
    char* _data;
};

// 使用示例
CharArray ca("hello");
ca[0] = 'H';  // 调用非const版本
cout << ca[0] << endl;  // 输出：H
````

**说明**：
- 提供const和非const两个版本
- 返回引用支持修改操作

### 3.7 类型转换函数
````cpp path=5.运算符重载.md mode=EXCERPT
class Point{
public:
    Point(int x, int y) : _ix(x), _iy(y) {}
    
    // 类型转换函数：Point → int
    operator int(){
        return _ix + _iy;
    }
    
    // 类型转换函数：Point → Complex
    operator Complex(){
        return Complex(_ix, _iy);
    }
private:
    int _ix;
    int _iy;
};

// 使用示例
Point pt(1, 2);
int a = pt;        // 隐式转换，a = 3
Complex c = pt;    // 隐式转换
````

**说明**：
- 类型转换函数必须是成员函数
- 没有返回类型声明，没有参数
- 函数体中必须返回目标类型的值

## 4. 学习要点和注意事项

### 4.1 友元的特点⭐⭐⭐
1. **单向性**：A是B的友元，B不一定是A的友元
2. **非传递性**：A是B的友元，B是C的友元，A不一定是C的友元
3. **非继承性**：友元关系不能被继承
4. **破坏封装性**：谨慎使用，只在必要时使用

### 4.2 运算符重载规则⚠️
1. **操作数类型限制**：至少有一个操作数是自定义类型
2. **优先级不变**：重载后优先级和结合性保持不变
3. **操作数个数不变**：不能改变运算符的操作数个数
4. **不能有默认参数**：会改变操作数个数
5. **不能创造新运算符**：如`@`、`$`等

### 4.3 不能重载的运算符⚠️
- **带点的运算符**：`.`、`.*`、`::`、`?:`
- **sizeof**运算符

### 4.4 重载形式选择指南💡

**友元函数形式**：
- 不修改操作数的运算符（`+`、`-`、`*`、`/`）
- 输入输出流运算符（`<<`、`>>`）
- 具有对称性的运算符

**成员函数形式**：
- 修改操作数的运算符（`+=`、`-=`、`++`、`--`）
- 必须是成员函数的运算符（`=`、`[]`、`()`、`->`）
- 与类型密切相关的运算符

### 4.5 常见错误⚠️
1. **语义不一致**：重载`+`却实现减法功能
2. **返回类型错误**：该返回引用时返回值，该返回值时返回引用
3. **const正确性**：忘记在不修改对象的函数后加`const`
4. **效率问题**：过度使用后置++而非前置++

### 4.6 前向声明的使用
````cpp path=5.运算符重载.md mode=EXCERPT
//前向声明
class Point;

class Line{
public:
    float distance(const Point & lhs, const Point & rhs);  // 只声明
};

class Point{
public:
    Point(int x, int y) : _ix(x), _iy(y) {}
    friend float Line::distance(const Point & lhs, const Point & rhs);
private:
    int _ix;
    int _iy;
};

// 在Point类定义之后实现
float Line::distance(const Point & lhs, const Point & rhs){
    return sqrt((lhs._ix - rhs._ix)*(lhs._ix - rhs._ix) +
                (lhs._iy - rhs._iy)*(lhs._iy - rhs._iy));
}
````

**说明**：前向声明让编译器认识类名，但函数体必须在完整类定义之后实现

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **友元三形式**：普通函数友元、成员函数友元、友元类
2. **重载三形式**：友元函数、普通函数、成员函数
3. **运算符重载本质**：定义特殊名称的函数
4. **类型转换**：构造函数实现隐式转换，类型转换函数实现显式转换

### ⚠️ 重要规则记忆
1. **必须成员函数重载**：`=` `[]` `()` `->` `->*`
2. **不能重载**：带点运算符 + sizeof
3. **友元特性**：单向、非传递、非继承、破坏封装
4. **前置vs后置**：前置返回引用，后置返回值

### 💡 选择指南速记
```
不修改操作数 → 友元函数
修改操作数   → 成员函数
输入输出流   → 友元函数（左操作数是流）
必须成员函数 → 成员函数（=[]()->->*）
```

### 🎯 常考知识点
1. **友元的三种形式**及其使用场景
2. **运算符重载的规则**和限制
3. **前置++与后置++的区别**
4. **输入输出流运算符**为什么必须用友元
5. **类型转换函数的语法**和特点

### 📝 代码模板记忆

**加法运算符（友元）**：
```cpp
friend Type operator+(const Type& lhs, const Type& rhs);
```

**赋值运算符（成员）**：
```cpp
Type& operator+=(const Type& rhs) { /*修改*/ return *this; }
```

**输出流运算符（友元）**：
```cpp
friend ostream& operator<<(ostream& os, const Type& obj);
```

**类型转换函数（成员）**：
```cpp
operator TargetType() { return /*转换逻辑*/; }
```

### 🔧 实用技巧
1. **效率优先**：优先使用前置++而非后置++
2. **const正确性**：不修改对象的函数加const
3. **链式操作**：返回引用支持`a += b += c`
4. **语义一致**：重载运算符保持直观含义

### 📚 运算符重载步骤
1. **确定返回值类型**（根据运算符语义）
2. **写函数名**（operator + 运算符）
3. **确定参数列表**（考虑操作数个数和重载形式）
4. **实现函数体**（具体的运算逻辑）

### 🏗️ 类型转换优先级
当多种转换方式同时存在时：
```
赋值运算符函数 > 类型转换函数 > 隐式转换（特殊构造函数）
```

### 📊 能重载的运算符（42个）

| 算术      | 关系            | 逻辑      | 位运算         | 赋值                               | 其他                                           |
| --------- | --------------- | --------- | -------------- | ---------------------------------- | ---------------------------------------------- |
| + - * / % | == != < > <= >= | && \|\| ! | & \| ^ ~ << >> | = += -= *= /= %= ^= &= \|= <<= >>= | ++ -- , [] () -> ->* new delete new[] delete[] |

### 🌟 学习建议
1. **理论与实践结合**：每个概念都要写代码验证
2. **掌握选择原则**：什么时候用友元，什么时候用成员函数
3. **注意语义一致性**：重载的运算符要符合直觉
4. **练习常用运算符**：+、+=、++、<<、[]等
5. **理解友元机制**：在保持封装的前提下提供灵活性

### 🎓 进阶方向
掌握运算符重载基础后，可以学习：
- **智能指针实现**：深入理解->和*运算符
- **迭代器设计**：实现类似STL迭代器的功能
- **表达式模板**：高级模板技术优化运算符重载
- **函数对象**：重载()运算符实现可调用对象
- **RAII技术**：结合运算符重载实现资源管理

### 📋 调试技巧
1. **分步测试**：先测试简单的运算符重载
2. **检查返回类型**：确认是返回值还是引用
3. **验证const正确性**：测试const对象的操作
4. **观察调用过程**：在函数中添加输出语句
5. **测试链式操作**：验证返回值是否支持连续操作

### 💼 实际应用场景
1. **数学库**：复数、矩阵、向量运算
2. **字符串类**：实现类似std::string的功能
3. **智能指针**：自动内存管理
4. **容器类**：实现类似STL容器的接口
5. **日期时间类**：日期的加减运算
6. **大数运算**：超出内置类型范围的数值计算

### 🔍 常见面试题
1. **为什么输出流运算符必须用友元函数？**
   - 因为左操作数是流对象，不是自定义类对象
2. **前置++和后置++的区别？**
   - 返回类型、效率、实现方式都不同
3. **哪些运算符必须用成员函数重载？**
   - = [] () -> ->*
4. **友元破坏封装性，为什么还要使用？**
   - 在特定场景下提供必要的灵活性，如运算符重载
5. **如何实现一个安全的下标运算符？**
   - 提供const和非const两个版本，添加边界检查

这份笔记涵盖了运算符重载的核心内容，重点掌握友元机制、重载形式选择和常用运算符的实现方法，这些是C++面向对象编程的重要技能。