<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏 - 调试版本</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .debug-info {
            background: #333;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .game-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        canvas {
            border: 2px solid #00ff88;
            background: #0f0f23;
        }
        .controls {
            display: flex;
            gap: 10px;
        }
        button {
            padding: 10px 20px;
            background: #00ff88;
            color: black;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #00cc6a;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .score {
            font-size: 1.2em;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <h3>调试信息</h3>
        <div id="debug-log">正在初始化...</div>
    </div>

    <div class="game-container">
        <h1>🐍 贪吃蛇游戏 - 调试版本 🍎</h1>
        
        <div class="score">
            分数: <span id="score">0</span> | 
            最高分: <span id="high-score">0</span> | 
            时间: <span id="time">00:00</span>
        </div>

        <canvas id="game-canvas" width="600" height="600"></canvas>

        <div class="controls">
            <button id="start-btn">开始游戏</button>
            <button id="pause-btn" disabled>暂停</button>
            <button id="restart-btn">重新开始</button>
        </div>

        <div style="margin-top: 20px;">
            <p>使用方向键或WASD控制蛇的移动</p>
            <p>空格键暂停/继续游戏</p>
        </div>
    </div>

    <script>
        // 调试日志函数
        function debugLog(message) {
            console.log(message);
            const debugElement = document.getElementById('debug-log');
            debugElement.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
        }

        // 游戏配置
        const CONFIG = {
            CANVAS_SIZE: 600,
            GRID_SIZE: 20,
            SPEED: 150,
            COLORS: {
                snake: '#00ff88',
                food: '#ff6b6b',
                background: '#0f0f23'
            }
        };

        // 游戏状态
        let gameState = {
            snake: [{ x: 10, y: 10 }],
            direction: { x: 1, y: 0 },
            food: { x: 15, y: 15 },
            score: 0,
            highScore: parseInt(localStorage.getItem('snakeHighScore')) || 0,
            gameTime: 0,
            isPlaying: false,
            gameLoop: null,
            timeInterval: null
        };

        // 获取DOM元素
        let canvas, ctx;
        let startBtn, pauseBtn, restartBtn;
        let scoreElement, highScoreElement, timeElement;

        // 初始化游戏
        function initGame() {
            debugLog('开始初始化游戏...');
            
            try {
                // 获取canvas元素
                canvas = document.getElementById('game-canvas');
                if (!canvas) {
                    throw new Error('无法找到canvas元素');
                }
                
                ctx = canvas.getContext('2d');
                if (!ctx) {
                    throw new Error('无法获取canvas上下文');
                }
                
                debugLog('Canvas初始化成功');

                // 获取按钮元素
                startBtn = document.getElementById('start-btn');
                pauseBtn = document.getElementById('pause-btn');
                restartBtn = document.getElementById('restart-btn');
                
                if (!startBtn || !pauseBtn || !restartBtn) {
                    throw new Error('无法找到控制按钮');
                }
                
                debugLog('按钮元素获取成功');

                // 获取分数显示元素
                scoreElement = document.getElementById('score');
                highScoreElement = document.getElementById('high-score');
                timeElement = document.getElementById('time');
                
                if (!scoreElement || !highScoreElement || !timeElement) {
                    throw new Error('无法找到分数显示元素');
                }
                
                debugLog('分数显示元素获取成功');

                // 设置事件监听器
                setupEventListeners();
                debugLog('事件监听器设置完成');

                // 初始化显示
                updateDisplay();
                draw();
                debugLog('初始显示完成');

                debugLog('游戏初始化完成！');
                
            } catch (error) {
                debugLog('初始化错误: ' + error.message);
                console.error('初始化错误:', error);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 按钮事件
            startBtn.addEventListener('click', startGame);
            pauseBtn.addEventListener('click', togglePause);
            restartBtn.addEventListener('click', restartGame);

            // 键盘事件
            document.addEventListener('keydown', handleKeyPress);
        }

        // 处理键盘输入
        function handleKeyPress(e) {
            if (!gameState.isPlaying) return;

            switch (e.key) {
                case 'ArrowUp':
                case 'w':
                case 'W':
                    e.preventDefault();
                    if (gameState.direction.y === 0) {
                        gameState.direction = { x: 0, y: -1 };
                    }
                    break;
                case 'ArrowDown':
                case 's':
                case 'S':
                    e.preventDefault();
                    if (gameState.direction.y === 0) {
                        gameState.direction = { x: 0, y: 1 };
                    }
                    break;
                case 'ArrowLeft':
                case 'a':
                case 'A':
                    e.preventDefault();
                    if (gameState.direction.x === 0) {
                        gameState.direction = { x: -1, y: 0 };
                    }
                    break;
                case 'ArrowRight':
                case 'd':
                case 'D':
                    e.preventDefault();
                    if (gameState.direction.x === 0) {
                        gameState.direction = { x: 1, y: 0 };
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    togglePause();
                    break;
            }
        }

        // 开始游戏
        function startGame() {
            debugLog('开始游戏');
            gameState.isPlaying = true;
            startBtn.disabled = true;
            pauseBtn.disabled = false;
            
            // 重置游戏状态
            gameState.snake = [{ x: 10, y: 10 }];
            gameState.direction = { x: 1, y: 0 };
            gameState.score = 0;
            gameState.gameTime = 0;
            generateFood();
            
            // 开始游戏循环
            gameState.gameLoop = setInterval(gameLoop, CONFIG.SPEED);
            gameState.timeInterval = setInterval(() => {
                gameState.gameTime++;
                updateDisplay();
            }, 1000);
            
            updateDisplay();
        }

        // 暂停/继续游戏
        function togglePause() {
            if (gameState.isPlaying) {
                debugLog('暂停游戏');
                clearInterval(gameState.gameLoop);
                clearInterval(gameState.timeInterval);
                gameState.isPlaying = false;
                pauseBtn.textContent = '继续';
            } else {
                debugLog('继续游戏');
                gameState.gameLoop = setInterval(gameLoop, CONFIG.SPEED);
                gameState.timeInterval = setInterval(() => {
                    gameState.gameTime++;
                    updateDisplay();
                }, 1000);
                gameState.isPlaying = true;
                pauseBtn.textContent = '暂停';
            }
        }

        // 重新开始游戏
        function restartGame() {
            debugLog('重新开始游戏');
            clearInterval(gameState.gameLoop);
            clearInterval(gameState.timeInterval);
            gameState.isPlaying = false;
            startBtn.disabled = false;
            pauseBtn.disabled = true;
            pauseBtn.textContent = '暂停';
            
            // 重置游戏状态
            gameState.snake = [{ x: 10, y: 10 }];
            gameState.direction = { x: 1, y: 0 };
            gameState.score = 0;
            gameState.gameTime = 0;
            generateFood();
            
            updateDisplay();
            draw();
        }

        // 游戏主循环
        function gameLoop() {
            // 移动蛇
            const head = { ...gameState.snake[0] };
            head.x += gameState.direction.x;
            head.y += gameState.direction.y;

            // 检查边界碰撞
            const gridSize = CONFIG.CANVAS_SIZE / CONFIG.GRID_SIZE;
            if (head.x < 0 || head.x >= gridSize || head.y < 0 || head.y >= gridSize) {
                gameOver();
                return;
            }

            // 检查自身碰撞
            for (const segment of gameState.snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            gameState.snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === gameState.food.x && head.y === gameState.food.y) {
                gameState.score += 10;
                generateFood();
                debugLog('吃到食物，分数: ' + gameState.score);
            } else {
                gameState.snake.pop();
            }

            updateDisplay();
            draw();
        }

        // 生成食物
        function generateFood() {
            const gridSize = CONFIG.CANVAS_SIZE / CONFIG.GRID_SIZE;
            let validPosition = false;
            
            while (!validPosition) {
                gameState.food = {
                    x: Math.floor(Math.random() * gridSize),
                    y: Math.floor(Math.random() * gridSize)
                };
                
                validPosition = true;
                for (const segment of gameState.snake) {
                    if (segment.x === gameState.food.x && segment.y === gameState.food.y) {
                        validPosition = false;
                        break;
                    }
                }
            }
        }

        // 游戏结束
        function gameOver() {
            debugLog('游戏结束，最终分数: ' + gameState.score);
            clearInterval(gameState.gameLoop);
            clearInterval(gameState.timeInterval);
            gameState.isPlaying = false;
            
            if (gameState.score > gameState.highScore) {
                gameState.highScore = gameState.score;
                localStorage.setItem('snakeHighScore', gameState.highScore.toString());
                debugLog('新纪录！');
            }
            
            startBtn.disabled = false;
            pauseBtn.disabled = true;
            pauseBtn.textContent = '暂停';
            updateDisplay();
            
            alert('游戏结束！分数: ' + gameState.score);
        }

        // 更新显示
        function updateDisplay() {
            scoreElement.textContent = gameState.score;
            highScoreElement.textContent = gameState.highScore;
            
            const minutes = Math.floor(gameState.gameTime / 60);
            const seconds = gameState.gameTime % 60;
            timeElement.textContent = 
                minutes.toString().padStart(2, '0') + ':' + 
                seconds.toString().padStart(2, '0');
        }

        // 绘制游戏画面
        function draw() {
            // 清空画布
            ctx.fillStyle = CONFIG.COLORS.background;
            ctx.fillRect(0, 0, CONFIG.CANVAS_SIZE, CONFIG.CANVAS_SIZE);

            // 绘制蛇
            ctx.fillStyle = CONFIG.COLORS.snake;
            for (const segment of gameState.snake) {
                ctx.fillRect(
                    segment.x * CONFIG.GRID_SIZE + 1,
                    segment.y * CONFIG.GRID_SIZE + 1,
                    CONFIG.GRID_SIZE - 2,
                    CONFIG.GRID_SIZE - 2
                );
            }

            // 绘制食物
            ctx.fillStyle = CONFIG.COLORS.food;
            ctx.fillRect(
                gameState.food.x * CONFIG.GRID_SIZE + 1,
                gameState.food.y * CONFIG.GRID_SIZE + 1,
                CONFIG.GRID_SIZE - 2,
                CONFIG.GRID_SIZE - 2
            );
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('页面加载完成，开始初始化');
            initGame();
        });
    </script>
</body>
</html>
