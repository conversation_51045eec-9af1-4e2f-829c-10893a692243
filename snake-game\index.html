<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>贪吃蛇游戏 - Snake Game</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- 游戏标题 -->
        <header class="game-header">
            <h1 class="game-title">
                <span class="title-icon">🐍</span>
                贪吃蛇游戏
                <span class="title-icon">🍎</span>
            </h1>
        </header>

        <!-- 游戏信息面板 -->
        <div class="game-info">
            <div class="info-panel">
                <div class="score-display">
                    <span class="label">当前分数</span>
                    <span class="value" id="current-score">0</span>
                </div>
                <div class="score-display">
                    <span class="label">最高分数</span>
                    <span class="value" id="high-score">0</span>
                </div>
                <div class="score-display">
                    <span class="label">游戏时间</span>
                    <span class="value" id="game-time">00:00</span>
                </div>
                <div class="score-display">
                    <span class="label">道具</span>
                    <span class="value" id="active-powerups">无</span>
                </div>
            </div>
            
            <!-- 游戏设置 -->
            <div class="game-settings">
                <div class="setting-item">
                    <label for="difficulty">难度级别:</label>
                    <select id="difficulty" class="setting-select">
                        <option value="easy">简单</option>
                        <option value="medium" selected>中等</option>
                        <option value="hard">困难</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label for="theme">游戏主题:</label>
                    <select id="theme" class="setting-select">
                        <option value="neon" selected>霓虹</option>
                        <option value="classic">经典</option>
                        <option value="forest">森林</option>
                        <option value="ocean">海洋</option>
                        <option value="sunset">日落</option>
                        <option value="cyberpunk">赛博朋克</option>
                    </select>
                </div>

                <div class="setting-item">
                    <label for="game-mode">游戏模式:</label>
                    <select id="game-mode" class="setting-select">
                        <option value="classic" selected>经典模式</option>
                        <option value="time-attack">时间挑战</option>
                        <option value="survival">生存模式</option>
                        <option value="speed-run">竞速模式</option>
                        <option value="zen">禅意模式</option>
                        <option value="chaos">混沌模式</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 游戏画布容器 -->
        <div class="canvas-container">
            <canvas id="game-canvas" width="600" height="600"></canvas>
            
            <!-- 游戏覆盖层 -->
            <div class="game-overlay" id="game-overlay">
                <div class="overlay-content">
                    <h2 id="overlay-title">准备开始游戏</h2>
                    <p id="overlay-message">点击开始按钮开始游戏</p>
                    <div class="overlay-stats" id="overlay-stats" style="display: none;">
                        <div class="stat-item">
                            <span class="stat-label">最终分数:</span>
                            <span class="stat-value" id="final-score">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">游戏时长:</span>
                            <span class="stat-value" id="final-time">00:00</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">食物数量:</span>
                            <span class="stat-value" id="food-count">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="game-controls">
                <button id="start-btn" class="control-btn primary">
                    <span class="btn-icon">▶</span>
                    开始游戏
                </button>
                <button id="pause-btn" class="control-btn secondary" disabled>
                    <span class="btn-icon">⏸</span>
                    暂停
                </button>
                <button id="restart-btn" class="control-btn danger">
                    <span class="btn-icon">🔄</span>
                    重新开始
                </button>
                <button id="achievements-btn" class="control-btn info">
                    <span class="btn-icon">🏆</span>
                    成就
                </button>
                <button id="help-btn" class="control-btn help">
                    <span class="btn-icon">❓</span>
                    玩法说明
                </button>
            </div>

            <!-- 设置面板 -->
            <div class="settings-panel">
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="sound-toggle" checked>
                        <span class="checkmark"></span>
                        音效
                    </label>
                </div>
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="obstacles-toggle">
                        <span class="checkmark"></span>
                        障碍物
                    </label>
                </div>
                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="special-food-toggle" checked>
                        <span class="checkmark"></span>
                        特殊食物
                    </label>
                </div>
            </div>
        </div>

        <!-- 移动端控制器 -->
        <div class="mobile-controls" id="mobile-controls">
            <div class="direction-pad">
                <button class="direction-btn up" data-direction="up">
                    <span class="arrow">↑</span>
                </button>
                <div class="horizontal-controls">
                    <button class="direction-btn left" data-direction="left">
                        <span class="arrow">←</span>
                    </button>
                    <button class="direction-btn right" data-direction="right">
                        <span class="arrow">→</span>
                    </button>
                </div>
                <button class="direction-btn down" data-direction="down">
                    <span class="arrow">↓</span>
                </button>
            </div>
        </div>

        <!-- 游戏说明 -->
        <div class="game-instructions">
            <h3>游戏说明</h3>
            <div class="instructions-content">
                <div class="instruction-item">
                    <span class="instruction-icon">⌨️</span>
                    <span>使用方向键或WASD控制蛇的移动</span>
                </div>
                <div class="instruction-item">
                    <span class="instruction-icon">📱</span>
                    <span>移动端可使用屏幕方向键</span>
                </div>
                <div class="instruction-item">
                    <span class="instruction-icon">🍎</span>
                    <span>吃到红色食物获得10分</span>
                </div>
                <div class="instruction-item">
                    <span class="instruction-icon">⭐</span>
                    <span>金色食物获得50分并减速</span>
                </div>
                <div class="instruction-item">
                    <span class="instruction-icon">💎</span>
                    <span>蓝色食物获得30分并可穿墙</span>
                </div>
            </div>
        </div>

        <!-- 成就弹窗 -->
        <div class="achievement-popup" id="achievement-popup">
            <div class="achievement-content">
                <div class="achievement-header">
                    <h3>🏆 成就解锁</h3>
                    <button class="close-btn" id="close-achievement">&times;</button>
                </div>
                <div class="achievement-body">
                    <div class="achievement-icon" id="achievement-icon">🎯</div>
                    <div class="achievement-info">
                        <h4 id="achievement-title">成就标题</h4>
                        <p id="achievement-description">成就描述</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成就列表弹窗 -->
        <div class="achievements-modal" id="achievements-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🏆 成就系统</h3>
                    <button class="close-btn" id="close-achievements">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="achievements-stats">
                        <div class="stat-item">
                            <span class="stat-label">已解锁:</span>
                            <span class="stat-value" id="unlocked-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">总成就:</span>
                            <span class="stat-value" id="total-achievements">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">完成度:</span>
                            <span class="stat-value" id="completion-rate">0%</span>
                        </div>
                    </div>
                    <div class="achievements-list" id="achievements-list">
                        <!-- 成就列表将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 玩法说明弹窗 -->
        <div class="help-modal" id="help-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>❓ 玩法说明</h3>
                    <button class="close-btn" id="close-help">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="help-section">
                        <h4>🎮 游戏模式</h4>
                        <div class="help-grid">
                            <div class="help-item">
                                <span class="help-icon">🐍</span>
                                <div class="help-content">
                                    <h5>经典模式</h5>
                                    <p>传统贪吃蛇游戏，吃食物增长，避免撞墙和自己</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">⏰</span>
                                <div class="help-content">
                                    <h5>时间挑战</h5>
                                    <p>2分钟限时挑战，分数倍数1.5倍</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">💀</span>
                                <div class="help-content">
                                    <h5>生存模式</h5>
                                    <p>障碍物随机生成，考验长期生存能力</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">⚡</span>
                                <div class="help-content">
                                    <h5>竞速模式</h5>
                                    <p>速度逐渐增加，挑战反应极限</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🧘</span>
                                <div class="help-content">
                                    <h5>禅意模式</h5>
                                    <p>无边界无死亡，纯粹放松体验</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🌪️</span>
                                <div class="help-content">
                                    <h5>混沌模式</h5>
                                    <p>随机事件不断发生，充满惊喜</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="help-section">
                        <h4>🎁 道具系统</h4>
                        <div class="help-grid">
                            <div class="help-item">
                                <span class="help-icon">⚡</span>
                                <div class="help-content">
                                    <h5>加速道具</h5>
                                    <p>移动速度提升50%，持续8秒</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🐌</span>
                                <div class="help-content">
                                    <h5>减速道具</h5>
                                    <p>移动速度降低50%，持续6秒</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">👻</span>
                                <div class="help-content">
                                    <h5>穿墙道具</h5>
                                    <p>可以穿过墙壁和障碍物，持续10秒</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">💣</span>
                                <div class="help-content">
                                    <h5>炸弹道具</h5>
                                    <p>即时清除所有障碍物</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🧲</span>
                                <div class="help-content">
                                    <h5>磁铁道具</h5>
                                    <p>自动吸引附近食物，持续12秒</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🛡️</span>
                                <div class="help-content">
                                    <h5>护盾道具</h5>
                                    <p>免疫一次碰撞伤害，持续5秒</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">✖️</span>
                                <div class="help-content">
                                    <h5>倍增道具</h5>
                                    <p>分数获得翻倍，持续15秒</p>
                                </div>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">📉</span>
                                <div class="help-content">
                                    <h5>缩小道具</h5>
                                    <p>蛇身长度减少2节</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="help-section">
                        <h4>🎨 主题系统</h4>
                        <p>选择不同主题改变游戏视觉效果，包括颜色方案、光晕效果和粒子特效。</p>
                    </div>

                    <div class="help-section">
                        <h4>🏆 成就系统</h4>
                        <p>完成各种挑战解锁成就，包括连击、生存时间、高分等多种类型。</p>
                    </div>

                    <div class="help-section">
                        <h4>🎮 操作方式</h4>
                        <p><strong>键盘：</strong>方向键或WASD控制移动<br>
                        <strong>触屏：</strong>点击屏幕下方的方向按钮</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 音频元素 -->
    <audio id="eat-sound" preload="auto">
        <source src="sounds/eat.mp3" type="audio/mpeg">
        <source src="sounds/eat.wav" type="audio/wav">
    </audio>
    <audio id="game-over-sound" preload="auto">
        <source src="sounds/game-over.mp3" type="audio/mpeg">
        <source src="sounds/game-over.wav" type="audio/wav">
    </audio>
    <audio id="special-sound" preload="auto">
        <source src="sounds/special.mp3" type="audio/mpeg">
        <source src="sounds/special.wav" type="audio/wav">
    </audio>

    <script src="script.js"></script>
</body>
</html>
