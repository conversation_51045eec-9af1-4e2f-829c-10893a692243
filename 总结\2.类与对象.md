# C++类与对象 学习笔记

## 1. 面向对象思想基础

### 1.1 两种编程思想对比

#### 过程论（面向过程）
- **核心思想**：数据和逻辑分离，程序本质是过程
- **特点**：确定性强，适合简单问题
- **局限**：复杂系统难以理清思路

#### 对象论（面向对象）
- **核心思想**：数据和逻辑相互依存，形成对象
- **特点**：相对不确定，但便于分离关注
- **优势**：**更适合分析规模较大的事物**

### 1.2 类与对象的关系
- **类**：对现实世界相似事物的抽象（模板）
- **对象**：类的实例（具体的变量）
- **关系**：由对象抽象出类，由类实例化出对象

## 2. 类的定义

### 2.1 基本语法
````cpp path=2.类与对象.md mode=EXCERPT
class MyClass {
    void myFunc() {}  // 成员函数
    int _a;          // 数据成员
}; // 分号不能省略
````

### 2.2 类的组成
1. **数据成员**：相当于现实世界中的属性
2. **成员函数**：相当于现实世界中的行为

### 2.3 访问权限控制
- **public**：类外可访问
- **private**：类外不可访问（默认）
- **protected**：继承时使用

## 3. 构造函数（Constructor）⭐⭐⭐

### 3.1 构造函数的作用
- **目的**：完成数据成员的初始化及其他操作
- **调用时机**：对象创建时自动调用
- **特点**：函数名与类名相同，无返回值

### 3.2 对象创建规则

#### 规则1：默认构造函数
- 无显式构造函数时，编译器自动生成默认构造函数
- **注意**：默认构造函数不会初始化数据成员

#### 规则2：自定义构造函数
- 提供构造函数后，编译器不再生成默认构造函数

#### 规则3：构造函数重载
- 可以定义多个不同参数的构造函数

### 3.3 初始化列表（推荐方式）⭐
````cpp path=2.类与对象.md mode=EXCERPT
Point(int ix = 0, int iy = 0)
: _ix(ix)    // 初始化列表
, _iy(iy)
{
    cout << "Point(int,int)" << endl;
}
````

**重要特点**：
- 初始化顺序与声明顺序一致，与初始化列表顺序无关
- 推荐统一使用初始化列表进行数据成员初始化

## 4. 析构函数（Destructor）⭐⭐

### 4.1 析构函数的作用
- **目的**：回收数据成员申请的资源（如堆空间）
- **调用时机**：对象销毁时自动调用
- **特点**：函数名为 `~类名`，无参数，无返回值

### 4.2 析构函数调用时机
1. **全局对象**：程序结束时
2. **局部对象**：离开作用域时
3. **静态对象**：程序结束时
4. **堆对象**：使用delete时

### 4.3 指针数据成员示例
````cpp path=2.类与对象.md mode=EXCERPT
class Computer {
public:
    Computer(const char * brand, double price)
    : _brand(new char[strlen(brand) + 1]())
    , _price(price)
    {
        strcpy(_brand, brand);
    }
    
    ~Computer() {
        if(_brand) {
            delete [] _brand;
            _brand = nullptr;
        }
    }
private:
    char * _brand;
    double _price;
};
````

## 5. 对象的复制

### 5.1 拷贝构造函数
````cpp path=2.类与对象.md mode=EXCERPT
Point(const Point & rhs)  // 拷贝构造函数
: _ix(rhs._ix)
, _iy(rhs._iy)
{
    cout << "Point(const Point &)" << endl;
}
````

**调用时机**：
1. 用已存在对象初始化新对象
2. 函数参数为对象（值传递）
3. 函数返回对象（值返回）

### 5.2 赋值运算符函数
````cpp path=2.类与对象.md mode=EXCERPT
Point & operator=(const Point & rhs) {
    if(this != &rhs) {  // 自赋值检测
        _ix = rhs._ix;
        _iy = rhs._iy;
    }
    return *this;
}
````

### 5.3 三合成原则⭐⭐⭐
**如果需要手动定义其中一个，另外两个也需要手动定义**：
1. 拷贝构造函数
2. 赋值运算符函数  
3. 析构函数

## 6. 特殊的数据成员

### 6.1 常量数据成员
````cpp path=2.类与对象.md mode=EXCERPT
class Point {
private:
    const int _ix;  // 常量成员
    const int _iy;
public:
    Point(int ix, int iy)
    : _ix(ix)  // 必须在初始化列表中初始化
    , _iy(iy)
    {}
};
````

### 6.2 引用数据成员
- 必须在初始化列表中初始化
- 需要绑定已存在的变量

### 6.3 对象成员
````cpp path=2.类与对象.md mode=EXCERPT
class Line {
public:
    Line(int x1, int y1, int x2, int y2)
    : _pt1(x1, y1)  // 对象成员初始化
    , _pt2(x2, y2)
    {}
private:
    Point _pt1;  // 对象成员
    Point _pt2;
};
````

### 6.4 静态数据成员
- 属于类，不属于具体对象
- 所有对象共享同一份静态成员

## 7. const成员函数⭐⭐

### 7.1 定义和特点
````cpp path=2.类与对象.md mode=EXCERPT
void print() const {  // const成员函数
    cout << "(" << _ix << "," << _iy << ")" << endl;
}
````

**特点**：
1. 不能修改对象的数据成员
2. this指针被设置为双重const限定
3. const对象只能调用const成员函数

### 7.2 使用规则
1. const对象调用const成员函数
2. 非const对象优先调用非const版本
3. **建议**：确定不修改数据成员的函数都定义为const

## 8. 对象的内存布局

### 8.1 对象大小计算
- 成员函数不影响对象大小
- 对象大小与数据成员有关
- 存在**内存对齐**机制

### 8.2 内存对齐规则
- **规则**：按照类中占空间最大的数据成员大小的倍数对齐
- **目的**：提高CPU访问效率
- **代价**：可能浪费一些空间

## 9. 对象的组织方式

### 9.1 const对象
````cpp path=2.类与对象.md mode=EXCERPT
const Point pt(1,2);  // const对象
pt.print();  // 只能调用const成员函数
````

### 9.2 指向对象的指针
````cpp path=2.类与对象.md mode=EXCERPT
Point * p1 = &pt;        // 指向栈对象
Point * p2 = new Point(3,4);  // 指向堆对象
p1->print();  // 通过指针调用成员函数
````

### 9.3 对象数组
````cpp path=2.类与对象.md mode=EXCERPT
Point pts[2] = {Point(1,2), Point(3,4)};  // 对象数组
Point pts[] = {{1,2}, {3,4}};  // 简化写法
````

## 10. new/delete表达式的工作机制⭐⭐

### 10.1 new表达式的三个步骤
1. 调用operator new申请未类型化空间
2. 在该空间上调用构造函数初始化对象
3. 返回指向该对象的指针

### 10.2 delete表达式的两个步骤
1. 调用析构函数回收数据成员申请的资源
2. 调用operator delete回收对象所在空间

### 10.3 创建对象的条件探究
- **创建堆对象需要**：公有的operator new、构造函数
- **创建栈对象需要**：公有的构造函数、析构函数

## 11. 单例模式（设计模式）⭐⭐⭐

### 11.1 单例模式概念
- **目的**：确保一个类只有一个实例
- **应用场景**：全局资源管理、配置文件管理等

### 11.2 静态区单例实现
````cpp path=2.类与对象.md mode=EXCERPT
class Point {
public:
    static Point & getInstance() {
        static Point pt(1,2);  // 局部静态对象
        return pt;
    }
private:
    Point(int x, int y) : _ix(x), _iy(y) {}  // 私有构造
};
````

### 11.3 堆区单例实现（推荐）
````cpp path=2.类与对象.md mode=EXCERPT
class Point {
public:
    static Point * getInstance() {
        if(_pInstance == nullptr) {
            _pInstance = new Point(1,2);
        }
        return _pInstance;
    }
    
    static void destroy() {
        if(_pInstance) {
            delete _pInstance;
            _pInstance = nullptr;
        }
    }
private:
    Point(int x, int y) : _ix(x), _iy(y) {}
    ~Point() {}  // 私有析构
    static Point * _pInstance;
};

Point * Point::_pInstance = nullptr;  // 静态成员定义
````

## 📚 复习要点总结

### 🔥 重点掌握
1. **构造函数的作用和调用时机**
2. **初始化列表的使用方法**（推荐方式）
3. **析构函数的作用和调用时机**
4. **拷贝构造函数和赋值运算符函数的区别**
5. **三合成原则**的内容和重要性
6. **const成员函数的特点和使用规则**
7. **单例模式的实现方法**

### ⚠️ 注意事项
1. 类定义后必须加分号
2. 构造函数无返回值，析构函数无参数无返回值
3. 初始化顺序与声明顺序一致
4. const成员、引用成员、对象成员必须在初始化列表中初始化
5. 有指针成员时要注意深拷贝问题
6. 确定不修改数据成员的函数应定义为const

### 💡 面试常考
1. 构造函数和析构函数的调用时机
2. 拷贝构造函数的调用场景
3. 三合成原则的内容
4. const成员函数的作用
5. 单例模式的实现方式
6. new/delete表达式的工作步骤
7. 内存对齐的原理和目的

### 🎯 编程建议
1. 优先使用初始化列表初始化数据成员
2. 有指针成员时要实现深拷贝
3. 不修改数据成员的函数定义为const
4. 遵循三合成原则
5. 合理使用单例模式管理全局资源