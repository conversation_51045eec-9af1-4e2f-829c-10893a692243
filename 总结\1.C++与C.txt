# C++与C 学习笔记

## 1. 主要知识点概括

### 1.1 C++基础入门
- **文件扩展名**：C++源文件使用`.cc/.cpp`，头文件使用`.hh/.hpp`
- **编译环境**：使用`g++`编译器，安装命令`sudo apt install g++`
- **编译命令**：`g++ 文件名.cc/.cpp [-o name]`

### 1.2 Hello World程序分析
````cpp path=1.C++与C.md mode=EXCERPT
#include <iostream>
using namespace std;

int main(int argc, char * argv[]){
    cout << "hello,world" << endl;
    return 0;
}
````

**关键点说明**：
- `#include <iostream>`：C++标准库头文件（无后缀，模板相关）
- `using namespace std`：使用标准命名空间，简化代码书写
- `cout << ... << endl`：C++输出流语法，使用`<<`运算符

### 1.3 C++相对于C的核心新特性
1. **命名空间**：解决大型项目中的命名冲突问题
2. **引用**：变量的别名，提供更安全的指针替代方案
3. **const关键字增强**：更强的类型安全和常量定义
4. **new/delete**：C++的动态内存管理方式
5. **函数重载**：同名函数支持不同参数列表
6. **内联函数**：编译时展开的高效函数调用方式
7. **强制类型转换**：更安全的类型转换操作符

## 2. 重要概念定义和区别

### 2.1 命名空间的三种使用方式⭐⭐⭐

| 方式              | 语法                  | 优点               | 缺点             | 适用场景           |
| ----------------- | --------------------- | ------------------ | ---------------- | ------------------ |
| **作用域限定符**  | `wd::func()`          | 精确无冲突         | 书写繁琐         | 偶尔使用某个实体   |
| **using编译指令** | `using namespace wd;` | 书写简洁           | 可能污染命名空间 | 大量使用某命名空间 |
| **using声明语句** | `using wd::func;`     | 按需引入，相对安全 | 需逐个声明       | **推荐方式**       |

````cpp path=1.C++与C.md mode=EXCERPT
namespace wd {
    namespace cpp {
        int num = 200;
        void func() {
            cout << "cpp::func" << endl;
        }
    }
}

// 方式一：作用域限定符
void test0() {
    cout << wd::cpp::num << endl;
    wd::cpp::func();
}

// 方式二：using编译指令
void test1() {
    using namespace wd::cpp;
    cout << num << endl;
    func();
}

// 方式三：using声明语句
void test2() {
    using wd::cpp::num;
    using wd::cpp::func;
    cout << num << endl;
    func();
}
````

### 2.2 const修饰指针的三种形式⭐⭐⭐

````cpp path=1.C++与C.md mode=EXCERPT
const int * p1;        // 指向常量的指针
int const * p2;        // 指向常量的指针（第二种写法）
int * const p3;        // 常量指针
const int * const p4;  // 双重const限定
````

**记忆方法**：
- **const在*左边**：指向常量的指针（不能通过指针改变值，但可以改变指向）
- **const在*右边**：常量指针（不能改变指向，但可以通过指针改变值）

| 类型               | 能否改变指向 | 能否通过指针改变值 | 示例                  |
| ------------------ | ------------ | ------------------ | --------------------- |
| **指向常量的指针** | ✅ 可以       | ❌ 不可以           | `const int * p`       |
| **常量指针**       | ❌ 不可以     | ✅ 可以             | `int * const p`       |
| **双重const**      | ❌ 不可以     | ❌ 不可以           | `const int * const p` |

### 2.3 引用 vs 指针⭐⭐⭐

| 特性         | 引用           | 指针         |
| ------------ | -------------- | ------------ |
| **初始化**   | 必须初始化     | 可以不初始化 |
| **重新绑定** | 不能重新绑定   | 可以重新指向 |
| **空值**     | 不能为空       | 可以为NULL   |
| **运算**     | 不支持算术运算 | 支持算术运算 |
| **安全性**   | 更安全         | 相对危险     |
| **推荐度**   | **优先推荐**   | 必要时使用   |

````cpp path=1.C++与C.md mode=EXCERPT
void swap3(int & x, int & y) {  // 引用传递，不复制
    int temp = x;
    x = y;
    y = temp;
}

void swap2(int * px, int * py) {  // 地址传递，不复制
    int temp = *px;
    *px = *py;
    *py = temp;
}
````

### 2.4 new/delete vs malloc/free⭐⭐

| 特性         | malloc/free  | new/delete     |
| ------------ | ------------ | -------------- |
| **性质**     | 库函数       | 表达式         |
| **返回值**   | void*        | 相应类型的指针 |
| **初始化**   | 不会初始化   | 可以直接初始化 |
| **参数**     | 需要字节数   | 自动计算大小   |
| **类型安全** | 需要强制转换 | 类型安全       |

````cpp path=1.C++与C.md mode=EXCERPT
// malloc/free方式
int * p1 = (int*)malloc(sizeof(int));
*p1 = 10;
free(p1);

// new/delete方式
int * p2 = new int(10);    // 申请空间并初始化
delete p2;

// 数组申请
int * p3 = new int[10]();  // 申请数组并初始化为0
delete [] p3;
````

## 3. 关键代码示例说明

### 3.1 命名空间的定义和使用

````cpp path=1.C++与C.md mode=EXCERPT
namespace wd {
    int val1 = 0;
    char val2;
    void display() {
        cout << "wd::display()" << endl;
    }
}  // end of namespace wd
````

**要点说明**：
- 命名空间可以包含变量、常量、函数、结构体、类等实体
- 解决大型项目中的命名冲突问题
- 提供独立的作用域

### 3.2 引用的基本使用

````cpp path=1.C++与C.md mode=EXCERPT
void test0() {
    int num = 100;
    int & ref = num;  // 声明ref时进行了初始化（绑定）
    // int & ref2;    // error: 引用必须初始化
    
    cout << num << endl;   // 100
    cout << ref << endl;   // 100
    cout << &num << endl;  // 地址相同
    cout << &ref << endl;  // 地址相同
}
````

**要点说明**：
- 引用是变量的别名，逻辑上不占用额外内存
- 引用必须在声明时初始化
- 引用一经绑定不能更改
- 对引用取地址得到的是被引用变量的地址

### 3.3 函数重载示例

````cpp path=1.C++与C.md mode=EXCERPT
void swap(short & x, short & y) {
    short temp = x;
    x = y;
    y = temp;
}

void swap(int & x, int & y) {
    int temp = x;
    x = y;
    y = temp;
}

void swap(float & x, float & y) {
    float temp = x;
    x = y;
    y = temp;
}
````

**要点说明**：
- 同一作用域内，函数名相同但参数列表不同
- 编译器通过名字改编(name mangling)实现
- 减少函数名数量，提高代码可读性

### 3.4 默认参数的使用

````cpp path=1.C++与C.md mode=EXCERPT
void func(int x, int y = 20, int z = 30) {
    cout << "x = " << x << endl;
    cout << "y = " << y << endl;
    cout << "z = " << z << endl;
}

void test() {
    func(10);        // x=10, y=20, z=30
    func(10, 200);   // x=10, y=200, z=30
    func(10, 200, 300); // x=10, y=200, z=300
}
````

**要点说明**：
- 默认参数必须从右到左连续设置
- 调用时可以省略有默认值的参数
- 提高函数使用的灵活性

### 3.5 内联函数

````cpp path=1.C++与C.md mode=EXCERPT
inline int add(int x, int y) {
    return x + y;
}

void test() {
    int result = add(3, 5);  // 编译时可能展开为：int result = 3 + 5;
}
````

**要点说明**：
- 编译时展开，避免函数调用开销
- 适合短小、频繁调用的函数
- 只是建议，编译器可能忽略inline关键字

## 4. 学习要点和注意事项

### 4.1 命名空间使用建议⚠️
1. **提倡在命名空间中定义变量**，避免直接定义全局变量
2. **using声明建议用于局部作用域**，不要用于全局作用域
3. **不要在头文件中使用using编译指令**，避免命名空间污染
4. **头文件包含顺序**：自定义 → C头文件 → C++头文件 → 第三方库

### 4.2 const关键字注意事项⚠️
1. **const常量必须初始化**
2. **const常量优于宏定义**：有类型检查，更安全
3. **const常量只能用指向常量的指针指向**
4. **记住const修饰指针的规则**：看const相对于*的位置

### 4.3 引用使用规则⚠️
1. **引用必须初始化**，声明时就要绑定变量
2. **引用一经绑定不能更改**
3. **引用类型必须与绑定变量类型相同**
4. **不要返回局部变量的引用**（函数结束后变量销毁）
5. **谨慎返回堆空间变量的引用**（容易内存泄漏）

### 4.4 内存管理注意事项⚠️
1. **new/delete必须配对使用**
2. **new[]对应delete[]**
3. **避免重复delete**
4. **推荐使用valgrind检测内存泄漏**

### 4.5 函数相关注意事项⚠️
1. **函数重载依赖参数差异**，不能仅靠返回类型区分
2. **默认参数从右到左设置**
3. **内联函数适合短小常用的代码**
4. **内联函数定义通常放在头文件中**

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **命名空间的三种使用方式**及使用建议
2. **const修饰指针的三种形式**及区别
3. **引用的概念、特点和作为函数参数的优势**
4. **new/delete与malloc/free的区别**
5. **函数重载的条件和实现原理**
6. **内联函数的作用和使用场景**

### ⚠️ 易错点提醒
1. **const常量必须初始化**
2. **引用必须初始化且不能重新绑定**
3. **using编译指令建议用于局部作用域**
4. **new[]必须用delete[]释放**
5. **不要返回局部变量的引用**

### 💡 最佳实践
1. **优先使用引用而非指针作为函数参数**
2. **用const常量替代宏定义常量**
3. **用内联函数替代宏函数**
4. **使用using声明语句而非using编译指令**
5. **用new/delete替代malloc/free**

### 🎯 面试常考
1. **const常量 vs 宏定义常量的区别**
2. **引用 vs 指针的联系与区别**
3. **malloc/free vs new/delete的区别**
4. **函数重载的实现原理（名字改编）**
5. **内存布局的五个区域及特点**

### 📝 内存布局（从高到低地址）
1. **栈区**：局部变量，系统控制，高→低增长
2. **堆区**：动态分配，程序员控制，低→高增长
3. **全局/静态区**：全局变量、静态变量
4. **文字常量区**：字符串常量等
5. **程序代码区**：函数二进制代码

### 🔧 实用技巧
1. **valgrind检测内存泄漏**：`valgrind --tool=memcheck ./a.out`
2. **查看函数名字改编**：`g++ -c file.cc` + `nm file.o`
3. **extern "C"**：让C++函数按C方式编译
4. **强制类型转换**：优先使用`static_cast<>()`

### 📚 语法速查

#### 命名空间
````cpp path=1.C++与C.md mode=EDIT
// 定义命名空间
namespace name {
    // 实体定义
}

// 使用方式
name::entity;              // 作用域限定符
using namespace name;      // using编译指令
using name::entity;        // using声明语句
````

#### 引用语法
````cpp path=1.C++与C.md mode=EDIT
int num = 10;
int & ref = num;           // 引用定义
void func(int & param);    // 引用作为参数
const int & cref = num;    // 常引用
````

#### const指针
````cpp path=1.C++与C.md mode=EDIT
const int * p1;            // 指向常量的指针
int * const p2;            // 常量指针
const int * const p3;      // 双重const限定
````

#### 动态内存管理
````cpp path=1.C++与C.md mode=EDIT
int * p = new int(10);     // 申请并初始化
delete p;                  // 释放

int * arr = new int[10](); // 申请数组
delete [] arr;             // 释放数组
````

#### 函数重载
````cpp path=1.C++与C.md mode=EDIT
void func(int x);          // 重载函数1
void func(double x);       // 重载函数2
void func(int x, int y);   // 重载函数3
````

### 🌟 学习建议
1. **多练习代码**：理论结合实践
2. **注意细节**：C++语法比C更严格
3. **养成好习惯**：使用引用、const、智能指针
4. **理解原理**：不仅知道怎么用，还要知道为什么
5. **循序渐进**：这些是面向对象编程的基础

### 🎓 进阶方向
- 掌握这些基础后，可以学习：
  - 类和对象
  - 构造函数和析构函数
  - 运算符重载
  - 继承和多态
  - 模板编程
  - STL标准库

这份笔记涵盖了C++相对于C语言的主要增强特性，是学习C++面向对象编程的重要基础。建议重点掌握命名空间、引用、const关键字的使用，这些是后续学习的基石。