# 模板 学习笔记

## 1. 主要知识点概括

### 1.1 模板的基本概念
- **定义**：一种通用的描述机制，使用通用类型来定义函数或类
- **本质**：将数据类型作为参数，实现类型参数化
- **编程范式**：泛型编程（通用编程）
- **作用**：实现真正的代码可重用性，避免重复编写相似代码

### 1.2 为什么需要模板
**强类型语言的限制**：
- C++是编译型、强类型、静态语言
- 类型在编译时确定，进行严格类型检查
- 传统解决方案：函数重载（需要为每种类型写一个函数）

**模板的优势**：
- 避免编写大量重载函数
- 编译器根据调用自动生成对应函数
- 更"智能"的代码重用机制

### 1.3 模板的分类
1. **函数模板**：参数化的函数
2. **类模板**：参数化的类
3. **可变参数模板**：支持任意数量参数的模板

### 1.4 模板的工作机制⭐⭐⭐
- **时机**：编译时处理
- **本质**：代码生成器
- **过程**：模板 → 实例化 → 生成具体代码 → 编译 → 链接

## 2. 重要概念辨析

### 2.1 模板相关术语对比

| 术语         | 定义                               | 举例                                                |
| ------------ | ---------------------------------- | --------------------------------------------------- |
| **函数模板** | **通用的函数描述**                 | **`template<class T> T add(T,T)`**                  |
| **模板函数** | **由函数模板实例化生成的具体函数** | **`int add(int,int)`、`double add(double,double)`** |
| **类模板**   | **通用的类描述**                   | **`template<class T> class Stack`**                 |
| **模板类**   | **由类模板实例化生成的具体类**     | **`Stack<int>`、`Stack<string>`**                   |

### 2.2 实例化方式对比

| 实例化方式     | 语法                | 特点                   | 适用场景                   |
| -------------- | ------------------- | ---------------------- | -------------------------- |
| **隐式实例化** | **`add(1,2)`**      | **编译器自动推导类型** | **参数类型明确时**         |
| **显式实例化** | **`add<int>(1,2)`** | **手动指定类型**       | **需要类型转换或消除歧义** |

### 2.3 编译型语言 vs 解释型语言

| 特性         | 编译型语言(C++)  | 解释型语言(Python) |
| ------------ | ---------------- | ------------------ |
| **类型系统** | **强类型、静态** | **弱类型、动态**   |
| **类型检查** | **编译时**       | **运行时**         |
| **错误发现** | **编译期**       | **运行期**         |
| **灵活性**   | **较低**         | **较高**           |
| **性能**     | **较高**         | **较低**           |

## 3. 关键代码示例说明

### 3.1 函数模板基础

#### 传统函数重载方式
````cpp path=9.模板.md mode=EXCERPT
// 传统方式：需要为每种类型写一个函数
int add(int x, int y) { return x + y; }
double add(double x, double y) { return x + y; }
long add(long x, long y) { return x + y; }
string add(string x, string y) { return x + y; }
````

#### 函数模板方式
````cpp path=9.模板.md mode=EXCERPT
// 模板方式：一个模板解决所有类型
template <class T>  // 或 template <typename T>
T add(T x, T y) {
    return x + y;
}

int main() {
    cout << add(1, 2) << endl;        // 隐式实例化：int版本
    cout << add(1.2, 3.4) << endl;    // 隐式实例化：double版本
    cout << add<int>(1, 2) << endl;   // 显式实例化：指定int类型
    return 0;
}
````

**说明**：
- `template <class T>`：声明模板参数T
- `class`和`typename`关键字等价
- 编译器根据调用参数自动推导类型
- 一个模板可以生成多个不同类型的函数

### 3.2 函数模板的实例化

#### 隐式实例化示例
````cpp path=9.模板.md mode=EXCERPT
template <class T>
T add(T t1, T t2) { 
    return t1 + t2; 
}

void test0() {
    short s1 = 1, s2 = 2;
    int i1 = 3, i2 = 4;
    long l1 = 5, l2 = 6;
    double d1 = 1.1, d2 = 2.2;
    
    cout << "add(s1,s2): " << add(s1,s2) << endl;  // 生成short版本
    cout << "add(i1,i2): " << add(i1,i2) << endl;  // 生成int版本
    cout << "add(l1,l2): " << add(l1,l2) << endl;  // 生成long版本
    cout << "add(d1,d2): " << add(d1,d2) << endl;  // 生成double版本
}
````

**说明**：
- 编译器根据参数类型自动推导T的类型
- 实际生成了4个不同的模板函数
- 每种类型只生成一次，重复调用不会重复生成

#### 显式实例化解决类型冲突
````cpp path=9.模板.md mode=EXCERPT
template <class T>
T add(T t1, T t2) { 
    return t1 + t2; 
}

void test() {
    short s1 = 1;
    int i2 = 4;
    
    // cout << add(s1, i2) << endl;  // ❌ 错误：类型不匹配
    cout << add<int>(s1, i2) << endl;  // ✅ 正确：显式指定为int，s1自动转换
    
    int i1 = 4;
    double d2 = 5.3;
    cout << add<int>(i1, d2) << endl;  // ⚠️ 注意：d2会被截断为int
}
````

**说明**：
- 参数类型不同时无法自动推导
- 显式实例化可以指定类型，触发类型转换
- 注意类型转换可能导致精度损失

### 3.3 函数模板重载

#### 模板与模板重载
````cpp path=9.模板.md mode=EXCERPT
// 模板参数个数不同的重载
template <class T>  // 模板一：单类型参数
T add(T t1, T t2) { 
    return t1 + t2; 
}

template <class T1, class T2>  // 模板二：双类型参数
T1 add(T1 t1, T2 t2) {
    return t1 + t2;
}

void test() {
    double x = 9.1;
    int y = 10;
    
    cout << add(x, y) << endl;   // 调用模板二，返回double
    cout << add(y, x) << endl;   // 调用模板二，返回int（注意精度损失）
    
    // 显式实例化
    cout << add<int, int>(x, y) << endl;  // 强制使用模板二
    cout << add<int>(x, y) << endl;       // 使用模板一，x转为int
}
````

**说明**：
- 编译器选择参数匹配度最高的模板
- 返回类型与第一个参数类型相同
- 谨慎使用模板重载，容易产生歧义

#### 模板与普通函数重载
````cpp path=9.模板.md mode=EXCERPT
// 函数模板
template <class T1, class T2>
T1 add(T1 t1, T2 t2) {
    return t1 + t2;
}

// 普通函数
short add(short s1, short s2) {
    cout << "add(short,short)" << endl;
    return s1 + s2;
}

void test() {
    short s1 = 1, s2 = 2;
    cout << add(s1, s2) << endl;   // 调用普通函数（优先级更高）
}
````

**重要原则**：
- **普通函数优先于函数模板执行**
- 原因：普通函数更直接，效率更高
- 模板需要实例化过程，增加编译时间

### 3.4 模板的分离编译问题⭐⭐⭐

#### 问题演示
````cpp path=9.模板.md mode=EXCERPT
// add.h
template <class T>
T add(T t1, T t2);

// add.cc
#include "add.h"
template <class T>
T add(T t1, T t2) {
    return t1 + t2;
}

// testAdd.cc
#include "add.h"
void test0() {
    int i1 = 1, i2 = 2;
    cout << add(i1, i2) << endl;  // ❌ 链接错误：未定义
}
````

**问题原因**：
- 单独编译实现文件时，没有调用模板，不会生成函数
- 测试文件中只有声明，没有实现
- 链接时找不到函数定义

#### 解决方案
````cpp path=9.模板.md mode=EXCERPT
// add.h
template <class T>
T add(T t1, T t2);

#include "add.cc"  // 在头文件中包含实现文件

// 或者直接在头文件中实现
template <class T>
T add(T t1, T t2) {
    return t1 + t2;
}
````

**核心原则**：
- **模板使用时必须看到完整实现**
- **模板没有头文件和实现文件的区别**
- **C++标准库头文件无后缀名的原因**

### 3.5 模板特化

#### 基本特化语法
````cpp path=9.模板.md mode=EXCERPT
// 通用模板
template <class T>
T add(T t1, T t2) {
    return t1 + t2;
}

// 特化模板：处理C风格字符串
template <>  // template后跟空的<>
const char* add<const char*>(const char* p1, const char* p2) {
    char* ptmp = new char[strlen(p1) + strlen(p2) + 1]();
    strcpy(ptmp, p1);
    strcat(ptmp, p2);
    return ptmp;
}

void test() {
    const char* p = add("hello", ",world");
    cout << p << endl;  // 输出：hello,world
    delete[] p;
}
````

**特化要点**：
- 必须先有基础的函数模板
- `template <>`表示这是特化版本
- 函数名后指定特化的类型
- 解决通用模板无法处理的特殊情况

### 3.6 模板参数类型

#### 类型参数和非类型参数
````cpp path=9.模板.md mode=EXCERPT
// 类型参数
template <class T>
void func1(T t) { }

// 非类型参数（必须是整型）
template <int N>
void func2() {
    int arr[N];  // 编译时确定数组大小
}

// 混合参数
template <class T, int SIZE>
class Array {
private:
    T data[SIZE];
public:
    int size() const { return SIZE; }
};

void test() {
    func2<10>();           // N = 10
    Array<int, 100> arr;   // T = int, SIZE = 100
}
````

**非类型参数限制**：
- 必须是整型：char、short、int、long、size_t等
- 不能是浮点型：float、double不可以
- 编译时必须确定值

### 3.7 类模板

#### 基本类模板定义
````cpp path=9.模板.md mode=EXCERPT
template <class T, int kCapacity = 10>  // 默认参数
class Stack {
public:
    Stack() : _top(-1), _data(new T[kCapacity]()) {
        cout << "Stack()" << endl;
    }
    
    ~Stack() {
        if(_data) {
            delete[] _data;
            _data = nullptr;
        }
        cout << "~Stack()" << endl;
    }
    
    bool empty() const;
    bool full() const;
    void push(const T&);
    void pop();
    T top();
    
private:
    int _top;
    T* _data;
};
````

#### 类模板成员函数的类外实现
````cpp path=9.模板.md mode=EXCERPT
// 类外实现需要带上模板声明
template <class T, int kCapacity>  // 注意：默认参数不要重复写
bool Stack<T, kCapacity>::empty() const {
    return _top == -1;
}

template <class T, int kCapacity>
void Stack<T, kCapacity>::push(const T& value) {
    if(!full()) {
        _data[++_top] = value;
    }
}
````

#### 类模板的使用
````cpp path=9.模板.md mode=EXCERPT
void test() {
    Stack<int> intStack;        // 使用默认容量10
    Stack<string, 20> strStack; // 指定容量20
    
    intStack.push(1);
    intStack.push(2);
    
    strStack.push("hello");
    strStack.push("world");
}
````

**类模板特点**：
- 必须显式指定模板参数（不能自动推导）
- 可以有默认模板参数
- 成员函数只有被调用时才会实例化

### 3.8 成员函数模板

````cpp path=9.模板.md mode=EXCERPT
class Point {
public:
    Point(double x, double y) : _x(x), _y(y) {}
    
    // 成员函数模板
    template <class T>
    T add(T t1) {
        return _x + _y + t1;
    }
    
private:
    double _x;
    double _y;
};

// 类外实现成员函数模板
template <class T>
T Point::add(T t1) {
    return _x + _y + t1;
}

void test() {
    Point pt(1.5, 3.8);
    cout << pt.add(8.8) << endl;    // 调用模板成员函数
}
````

**成员函数模板限制**：
- **不能是虚函数**：模板在编译时生成，虚函数在运行时决定
- 可以访问类的数据成员
- 类外实现需要带上模板声明

### 3.9 可变参数模板

#### 基本语法
````cpp path=9.模板.md mode=EXCERPT
template <class ...Args>  // Args是模板参数包
void display(Args ...args) {  // args是函数参数包
    // 输出参数个数
    cout << "sizeof...(Args) = " << sizeof...(Args) << endl;
    cout << "sizeof...(args) = " << sizeof...(args) << endl;
}

void test() {
    display();                          // 0个参数
    display(1, "hello", 3.3, true, 5); // 5个参数
}
````

#### 递归展开参数包
````cpp path=9.模板.md mode=EXCERPT
// 递归出口
void print() {
    cout << endl;
}

// 可变参数模板
template <class T, class... Args>
void print(T x, Args... args) {
    cout << x << " ";
    print(args...);  // 递归调用，参数包逐个展开
}

void test() {
    print(1, "hello", 3.6, true, 100);
    // 输出：1 hello 3.6 1 100
}
````

**可变参数模板要点**：
- `...`在左边表示打包，在右边表示解包
- 需要递归出口（普通函数或普通模板）
- 推荐使用普通函数作为递归出口

## 4. 学习要点和注意事项

### 4.1 模板使用规则⚠️

**重要原则**：
1. **谨慎使用多个通用模板重载**
2. **优先使用隐式实例化**，让编译器推导类型
3. **只在必要时指定类型参数**
4. **模板定义通常放在头文件中**

### 4.2 模板的限制

**函数模板限制**：
- 参数类型必须支持模板中使用的操作
- 类型推导可能失败，需要显式指定
- 编译错误信息可能复杂难懂

**类模板限制**：
- 必须显式指定模板参数
- 成员函数只有被调用才实例化
- 模板参数必须在编译时确定

### 4.3 性能考虑💡

**优势**：
- 编译时生成，运行时无额外开销
- 类型安全，编译期检查
- 代码复用，减少重复编写

**劣势**：
- 增加编译时间
- 可能导致代码膨胀
- 调试困难

### 4.4 模板特化使用场景

**适用情况**：
- 通用模板无法处理某些特殊类型
- 需要为特定类型提供优化实现
- 处理指针、数组等特殊情况

**注意事项**：
- 必须先有通用模板
- 特化版本要与原模板兼容
- 避免过度特化

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **模板定义**：类型参数化，实现代码重用
2. **工作机制**：编译时代码生成器
3. **两种模板**：函数模板、类模板
4. **实例化方式**：隐式（自动推导）、显式（手动指定）

### ⚠️ 重要规则记忆
1. **模板发生时机**：编译时，不是运行时
2. **分离编译问题**：模板使用必须看到完整实现
3. **普通函数优先级**：高于函数模板
4. **类模板实例化**：必须显式指定类型参数

### 💡 关键语法点
```cpp
// 函数模板
template <class T>
T func(T t) { return t; }

// 类模板
template <class T, int N = 10>
class Container { };

// 特化模板
template <>
void func<int>(int t) { }

// 可变参数模板
template <class... Args>
void func(Args... args) { }
```

### 🎯 常考知识点
1. **模板的工作原理**和时机
2. **隐式vs显式实例化**的区别
3. **模板分离编译问题**及解决方案
4. **函数模板重载**的匹配规则
5. **模板特化**的语法和使用场景
6. **类模板成员函数**的实现方式

### 📝 代码模板记忆

**函数模板基础**：
````cpp path=9.模板.md mode=EDIT
template <class T>
T add(T a, T b) {
    return a + b;
}

// 调用
add(1, 2);          // 隐式实例化
add<double>(1, 2);  // 显式实例化
````

**类模板基础**：
````cpp path=9.模板.md mode=EDIT
template <class T>
class Stack {
public:
    void push(const T& item);
    T pop();
private:
    T* data;
};

// 使用
Stack<int> intStack;
Stack<string> strStack;
````

**模板特化**：
````cpp path=9.模板.md mode=EDIT
// 通用模板
template <class T>
void func(T t) { }

// 特化版本
template <>
void func<int>(int t) { }
````

### 🔧 实用技巧
1. **优先使用隐式实例化**：代码更简洁
2. **模板定义放头文件**：避免分离编译问题
3. **使用override检查特化**：确保正确特化
4. **合理使用默认参数**：提高模板易用性
5. **避免过度模板化**：平衡灵活性和复杂性

### 📚 学习顺序建议
1. 理解模板的概念和价值
2. 掌握函数模板的基本语法
3. 学习模板实例化机制
4. 了解模板分离编译问题
5. 掌握类模板的使用
6. 学习模板特化技术
7. 了解可变参数模板

### 🎓 实际应用场景
- **STL容器**：vector、list、map等
- **算法库**：sort、find等通用算法
- **智能指针**：shared_ptr、unique_ptr
- **数学库**：矩阵运算、数值计算
- **序列化**：通用的对象序列化框架

### ⚡ 与其他特性的关系
- **继承**：模板类可以继承，也可以被继承
- **多态**：模板与虚函数结合使用
- **重载**：模板函数可以重载
- **特化**：为特定类型提供定制实现

### 🌟 高级话题预览
- **模板元编程**：编译时计算
- **SFINAE**：替换失败不是错误
- **概念(Concepts)**：C++20的类型约束
- **变长模板**：处理任意数量参数

这份笔记涵盖了C++模板的核心内容，重点掌握函数模板和类模板的基本使用，理解模板的工作机制，这些是现代C++编程的重要技能。模板是STL的基础，也是泛型编程的核心工具。