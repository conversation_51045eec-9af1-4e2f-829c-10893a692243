# 多态 学习笔记

## 1. 主要知识点概括

### 1.1 多态的基本概念
- **定义**：对于同一个消息、同一种调用，在不同的场合、不同的情况下，执行不同的行为
- **本质**：一个接口，多种方法
- **意义**：面向对象编程的精髓，实现代码的复用性和可扩展性
- **生活例子**：警车鸣笛，普通人反应一般，逃犯听见会拔腿就跑

### 1.2 多态的分类
C++支持两种多态性：

**编译时多态（静态多态）**：
- **实现方式**：函数重载、运算符重载
- **决定时机**：编译期间根据参数和函数名决定
- **联编方式**：静态联编

**运行时多态（动态多态）**：
- **实现方式**：虚函数机制
- **决定时机**：程序运行时完成选择
- **联编方式**：动态联编（dynamic binding）

### 1.3 多态的价值
1. **代码重用**：与封装、继承共同实现代码复用
2. **降低耦合**：解决项目中紧耦合问题
3. **提高扩展性**：增加新的子类不影响已存在类的特性
4. **灵活性**：同一接口可以有多种实现

## 2. 重要概念辨析

### 2.1 虚函数 vs 普通函数

| 特性         | 普通函数       | 虚函数                      |
| ------------ | -------------- | --------------------------- |
| **调用方式** | **直接调用**   | **通过虚表间接调用**        |
| **内存开销** | **无额外开销** | **增加虚函数指针（8字节）** |
| **继承行为** | **发生隐藏**   | **发生覆盖**                |
| **多态支持** | **不支持**     | **支持动态多态**            |
| **性能**     | **更快**       | **略慢（间接调用）**        |

### 2.2 隐藏 vs 覆盖 vs 重载

| 概念               | 发生场景           | 条件                         | 效果                       |
| ------------------ | ------------------ | ---------------------------- | -------------------------- |
| **重载(overload)** | **同一作用域**     | **函数名相同，参数不同**     | **根据参数选择函数**       |
| **隐藏(oversee)**  | **基类派生类之间** | **函数名相同（参数可不同）** | **派生类函数隐藏基类函数** |
| **覆盖(override)** | **基类派生类之间** | **虚函数形式完全相同**       | **覆盖虚表中的地址**       |

### 2.3 虚函数的三个关键词
1. **存在**：虚函数确实存在于内存中
2. **间接**：通过虚函数指针和虚表间接访问
3. **共享**：基类指针可以调用派生类的虚函数实现

## 3. 关键代码示例说明

### 3.1 虚函数的基本使用

#### 无虚函数的情况
````cpp path=8.多态.md mode=EXCERPT
class Base {
public:
    Base(long x) : _base(x) {}
    void display() const {  // 普通函数
        cout << "Base::display()" << endl;
    }
private:
    long _base;
};

class Derived : public Base {
public:
    Derived(long base, long derived) : Base(base), _derived(derived) {}
    void display() const {  // 隐藏基类函数
        cout << "Derived::display()" << endl;
    }
private:
    long _derived;
};

void print(Base* pbase) {
    pbase->display();  // 总是调用Base::display()
}

void test() {
    Base base(10);
    Derived dd(1, 2);
    
    print(&base);  // 输出：Base::display()
    print(&dd);    // 输出：Base::display() (不是期望的结果)
}
````

#### 使用虚函数实现多态
````cpp path=8.多态.md mode=EXCERPT
class Base {
public:
    Base(long x) : _base(x) {}
    virtual void display() const {  // 虚函数
        cout << "Base::display()" << endl;
    }
private:
    long _base;
};

class Derived : public Base {
public:
    Derived(long base, long derived) : Base(base), _derived(derived) {}
    void display() const override {  // 覆盖虚函数
        cout << "Derived::display()" << endl;
    }
private:
    long _derived;
};

void print(Base* pbase) {
    pbase->display();  // 动态调用，根据实际对象类型决定
}

void test() {
    Base base(10);
    Derived dd(1, 2);
    
    print(&base);  // 输出：Base::display()
    print(&dd);    // 输出：Derived::display() (实现了多态)
}
````

**说明**：
- 添加`virtual`关键字后，基类指针能调用派生类的函数
- 对象大小增加了8字节（虚函数指针）
- 实现了真正的动态多态

### 3.2 虚函数的实现原理

#### 内存布局变化
````cpp path=8.多态.md mode=EXCERPT
// 虚函数机制的内存布局
class Base {
    virtual void func();
    long _data;
};
// 内存布局：[vfptr][_data]
// vfptr指向虚函数表，表中存储虚函数地址

class Derived : public Base {
    void func() override;  // 覆盖虚函数
    long _derived;
};
// 内存布局：[vfptr][_data][_derived]
// vfptr指向Derived的虚函数表，表中存储Derived::func的地址
````

**虚函数调用过程**：
1. 通过对象的虚函数指针(vfptr)找到虚函数表(vtable)
2. 在虚函数表中查找对应虚函数的地址
3. 调用该地址处的函数

### 3.3 动态多态的激活条件⭐⭐⭐

````cpp path=8.多态.md mode=EXCERPT
// 动态多态激活的5个条件示例
class Base {
public:
    virtual void func() {  // 1. 基类定义虚函数
        cout << "Base::func()" << endl;
    }
};

class Derived : public Base {
public:
    void func() override {  // 2. 派生类覆盖虚函数
        cout << "Derived::func()" << endl;
    }
};

void test() {
    Derived d;           // 3. 创建派生类对象
    Base* pb = &d;       // 4. 基类指针指向派生类对象
    pb->func();          // 5. 通过基类指针调用虚函数
    // 输出：Derived::func() (实现动态多态)
}
````

**激活条件总结**：
1. 基类定义虚函数
2. 派生类中要覆盖虚函数
3. 创建派生类对象
4. 基类的指针指向派生类对象（或基类引用绑定派生类对象）
5. 通过基类指针（引用）调用虚函数

### 3.4 虚函数的覆盖要求

````cpp path=8.多态.md mode=EXCERPT
class Base {
public:
    virtual int func(int x, double y) const {
        return x + y;
    }
};

class Derived : public Base {
public:
    // 正确的覆盖：完全相同的函数签名
    int func(int x, double y) const override {  // 推荐使用override
        return x * y;
    }
    
    // 错误示例（不是覆盖，而是隐藏）：
    // virtual int func(int x);           // 参数不同
    // virtual double func(int x, double y) const;  // 返回类型不同
    // virtual int func(int x, double y);  // const属性不同
};
````

**覆盖要求**：
- 相同的函数名
- 相同的参数个数和类型
- 相同的返回类型
- 相同的const属性

**override关键字的作用**：
- 告诉编译器此函数要覆盖基类虚函数
- 编译器检查是否满足覆盖条件
- 如果不满足，编译报错，避免隐藏错误

### 3.5 纯虚函数和抽象类

````cpp path=8.多态.md mode=EXCERPT
// 抽象类：包含纯虚函数的类
class Shape {  // 抽象类
public:
    virtual string getName() const = 0;    // 纯虚函数
    virtual double getArea() const = 0;    // 纯虚函数
};

class Circle : public Shape {
public:
    Circle(double r) : _radius(r) {}
    
    string getName() const override {      // 必须实现
        return "Circle";
    }
    
    double getArea() const override {      // 必须实现
        return 3.14 * _radius * _radius;
    }
    
private:
    double _radius;
};

void display(Shape& shape) {  // 可以定义抽象类的引用
    cout << shape.getName() << "的面积是:" << shape.getArea() << endl;
}

void test() {
    // Shape s;     // ❌ 错误：抽象类不能实例化
    Circle c(5);    // ✅ 正确：派生类实现了所有纯虚函数
    display(c);     // 输出：Circle的面积是:78.5
}
````

**纯虚函数特点**：
- 语法：`virtual 函数声明 = 0;`
- 只有声明，没有实现（可以在类外提供实现）
- 包含纯虚函数的类是抽象类
- 抽象类不能实例化对象
- 派生类必须实现所有纯虚函数才能实例化

### 3.6 多重继承中的虚函数

````cpp path=8.多态.md mode=EXCERPT
class Base1 {
public:
    virtual void func1() { cout << "Base1::func1()" << endl; }
    virtual void func2() { cout << "Base1::func2()" << endl; }
};

class Base2 {
public:
    virtual void func3() { cout << "Base2::func3()" << endl; }
};

class Derived : public Base1, public Base2 {
public:
    void func1() override { cout << "Derived::func1()" << endl; }
    void func3() override { cout << "Derived::func3()" << endl; }
    virtual void func4() { cout << "Derived::func4()" << endl; }
};

void test() {
    Derived d;
    
    Base1* pb1 = &d;
    pb1->func1();  // 输出：Derived::func1()
    pb1->func2();  // 输出：Base1::func2()
    
    Base2* pb2 = &d;
    pb2->func3();  // 输出：Derived::func3()
}
````

**多重继承虚函数特点**：
- 每个有虚函数的基类都有自己的虚函数表
- 派生类的新虚函数加入第一个虚函数表
- 派生类覆盖基类虚函数时，所有相关虚表都会更新

## 4. 学习要点和注意事项

### 4.1 虚函数的限制⚠️

**不能设为虚函数的情况**：

1. **构造函数**：
   - 原因：虚函数机制需要先创建对象，但构造函数的作用就是创建对象
   - 矛盾：对象未完成初始化时无法使用虚函数机制

2. **静态成员函数**：
   - 原因：虚函数调用需要`this->vfptr->vtable->function`
   - 问题：静态函数没有this指针，无法访问vfptr

3. **内联函数**：
   - 原因：inline在编译期展开，虚函数在运行期决定
   - 冲突：时机不同，如果同时存在，inline失效

4. **普通函数**：
   - 原因：虚函数解决的是对象多态问题
   - 限制：普通函数不属于类，与多态无关

### 4.2 虚函数的性能考虑💡

**内存开销**：
- 每个对象增加一个虚函数指针（8字节）
- 每个类增加一个虚函数表

**时间开销**：
- 虚函数调用比普通函数调用慢（间接调用）
- 查表开销：vfptr → vtable → function

**优化建议**：
- 只在需要多态的地方使用虚函数
- 避免在性能关键路径上过度使用虚函数

### 4.3 构造和析构中的虚函数调用⚠️

````cpp path=8.多态.md mode=EXCERPT
class Base {
public:
    Base() { func(); }      // 构造函数中调用虚函数
    virtual ~Base() { func(); }  // 析构函数中调用虚函数
    virtual void func() { cout << "Base::func()" << endl; }
};

class Derived : public Base {
public:
    Derived() { cout << "Derived构造" << endl; }
    ~Derived() { cout << "Derived析构" << endl; }
    void func() override { cout << "Derived::func()" << endl; }
};

void test() {
    Derived d;  // 构造时调用Base::func()，不是Derived::func()
}  // 析构时也调用Base::func()，不是Derived::func()
````

**特殊行为说明**：
- **构造期间**：只能看到当前层及以上的部分，调用基类版本
- **析构期间**：派生类部分已析构，只能调用基类版本
- **表现为静态联编**：不是动态多态行为

### 4.4 虚拟继承 vs 虚函数💡

**相同点**：
- 都使用`virtual`关键字
- 都体现"存在、间接、共享"特性
- 都有额外的内存和性能开销

**不同点**：

| 特性         | 虚函数       | 虚拟继承         |
| ------------ | ------------ | ---------------- |
| **解决问题** | **实现多态** | **解决菱形继承** |
| **间接机制** | **虚函数表** | **虚基表**       |
| **共享内容** | **函数实现** | **基类对象**     |
| **使用场景** | **动态多态** | **多重继承**     |

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **多态定义**：一个接口，多种方法；同一调用，不同行为
2. **两种多态**：编译时多态（重载）、运行时多态（虚函数）
3. **虚函数机制**：vfptr → vtable → function的间接调用
4. **动态多态条件**：5个条件缺一不可

### ⚠️ 重要规则记忆
1. **覆盖要求**：函数名、参数、返回类型、const属性完全相同
2. **虚函数限制**：构造函数、静态函数、内联函数、普通函数不能是虚函数
3. **抽象类规则**：包含纯虚函数，不能实例化，派生类必须实现所有纯虚函数
4. **构造析构特殊性**：期间调用虚函数表现为静态联编

### 💡 关键技术点
```
虚函数声明：virtual 返回类型 函数名(参数) [const]
纯虚函数声明：virtual 返回类型 函数名(参数) = 0;
覆盖推荐：使用override关键字确保正确覆盖
多态调用：基类指针/引用 → 派生类对象 → 虚函数
```

### 🎯 常考知识点
1. **动态多态的激活条件**（5个条件）
2. **虚函数的实现原理**（虚表机制）
3. **覆盖vs隐藏vs重载**的区别
4. **纯虚函数和抽象类**的特性
5. **虚函数的限制**和原因分析
6. **多重继承中的虚函数**布局

### 📝 代码模板记忆

**基本虚函数**：
````cpp path=8.多态.md mode=EDIT
class Base {
public:
    virtual void func() { cout << "Base::func()" << endl; }
    virtual ~Base() {}  // 虚析构函数
};

class Derived : public Base {
public:
    void func() override { cout << "Derived::func()" << endl; }
};
````

**纯虚函数和抽象类**：
````cpp path=8.多态.md mode=EDIT
class Abstract {
public:
    virtual void pureFunc() = 0;  // 纯虚函数
    virtual ~Abstract() {}
};

class Concrete : public Abstract {
public:
    void pureFunc() override { /*实现*/ }
};
````

**多态使用模式**：
````cpp path=8.多态.md mode=EDIT
void polymorphicFunction(Base* ptr) {
    ptr->virtualFunc();  // 动态调用
}

Derived d;
polymorphicFunction(&d);  // 传入派生类对象地址
````

### 🔧 实用技巧
1. **总是使用override**：确保正确覆盖，避免隐藏
2. **虚析构函数**：基类析构函数应该是虚函数
3. **纯虚函数设计**：定义接口，强制派生类实现
4. **性能权衡**：只在需要多态的地方使用虚函数
5. **避免构造析构中调用虚函数**：行为不符合预期

### 📚 学习顺序建议
1. 理解多态的概念和价值
2. 掌握虚函数的基本语法
3. 理解虚函数的实现原理
4. 学习动态多态的激活条件
5. 掌握纯虚函数和抽象类
6. 了解虚函数的限制和注意事项
7. 练习多重继承中的虚函数

### 🎓 面试重点
- 解释动态多态的实现原理
- 说明虚函数表的工作机制
- 分析动态多态的激活条件
- 讨论虚函数的性能影响
- 解释纯虚函数和抽象类的作用
- 分析构造析构中虚函数的特殊行为

### ⚡ 实际应用场景
- **图形系统**：Shape基类，Circle、Rectangle派生类
- **文件系统**：File基类，TextFile、BinaryFile派生类
- **游戏开发**：GameObject基类，Player、Enemy派生类
- **设计模式**：策略模式、工厂模式、观察者模式

这份笔记涵盖了C++多态机制的核心内容，重点掌握虚函数机制、动态多态条件和实现原理，这些是面向对象编程的高级技能。