# 日志系统 学习笔记

## 1. 主要知识点概括

### 1.1 日志系统的重要性
- **系统运行轨迹记录**：日志是调试和维护系统的重要参考资料
- **错误跟踪分析**：通过日志推断错误出现的位置和原因
- **系统审计**：记录系统运行流程，用于后续分析
- **高可靠性保障**：在不允许系统终止的环境中，日志是唯一的调试手段

### 1.2 日志系统的分类
- **业务级别日志**：供终端用户分析业务过程
- **系统级别日志**：供开发者维护系统稳定性

### 1.3 日志系统设计原则
- **性能考虑**：避免对系统性能造成过大影响
- **信息筛选**：避免海量日志掩盖真实问题
- **灵活配置**：支持不同的输出目标和格式

## 2. 重要概念辨析

### 2.1 日志系统四大核心组件

| 组件         | 英文名       | 作用               | 职责                                           |
| ------------ | ------------ | ------------------ | ---------------------------------------------- |
| **记录器**   | **Category** | **日志来源**       | 产生日志的原始信息（内容、优先级、时间、位置） |
| **过滤器**   | **Priority** | **日志系统优先级** | 按条件过滤不需要的日志                         |
| **格式化器** | **Layout**   | **日志布局**       | 按指定格式格式化日志信息                       |
| **输出器**   | **Appender** | **日志目的地**     | 将日志记录到指定位置（文件、控制台等）         |

### 2.2 日志目的地类型对比

| Appender类型            | 用途             | 构造函数参数                               | 适用场景           |
| ----------------------- | ---------------- | ------------------------------------------ | ------------------ |
| **OstreamAppender**     | **输出到C++流**  | 目的地名、输出流指针                       | 控制台输出、调试   |
| **FileAppender**        | **输出到文件**   | 目的地名、文件名                           | 持久化日志存储     |
| **RollingFileAppender** | **回卷文件输出** | 目的地名、文件名、最大文件大小、回卷文件数 | 大量日志的空间管理 |

### 2.3 日志布局类型

| Layout类型        | 特点           | 使用场景             |
| ----------------- | -------------- | -------------------- |
| **BasicLayout**   | **默认布局**   | 简单测试，显示时间戳 |
| **PatternLayout** | **自定义格式** | 生产环境，可读性强   |

### 2.4 日志优先级等级

````cpp path=4.日志系统.md mode=EXCERPT
typedef enum {
    EMERG = 0,    // 紧急
    FATAL = 0,    // 致命
    ALERT = 100,  // 警报
    CRIT = 200,   // 严重
    ERROR = 300,  // 错误
    WARN = 400,   // 警告
    NOTICE = 500, // 注意
    INFO = 600,   // 信息
    DEBUG = 700,  // 调试
    NOTSET = 800  // 未设置（不可用）
} PriorityLevel;
````

**优先级规则**：数值越小优先级越高，只有日志优先级 ≥ 系统优先级时，日志才会被输出

## 3. 关键代码示例说明

### 3.1 log4cpp安装和编译
````bash path=4.日志系统.md mode=EXCERPT
# 安装步骤
$ tar xzvf log4cpp-1.1.4rc3.tar.gz
$ cd log4cpp
$ ./configure
$ make
$ sudo make install

# 编译指令
g++ log4cppTest.cc -llog4cpp -lpthread
````

### 3.2 动态库配置（重要）
````bash path=4.日志系统.md mode=EXCERPT
# 解决"找不到动态库"错误
cd /etc
sudo vim ld.so.conf
# 添加：/usr/local/lib
sudo ldconfig
````

### 3.3 完整示例代码解析
````cpp path=4.日志系统.md mode=EXCERPT
#include "log4cpp/Category.hh"
#include "log4cpp/Appender.hh"
#include "log4cpp/FileAppender.hh"
#include "log4cpp/OstreamAppender.hh"
#include "log4cpp/Layout.hh"
#include "log4cpp/BasicLayout.hh"
#include "log4cpp/Priority.hh"

int main(int argc, char** argv) {
    // 1. 创建输出器（目的地）
    log4cpp::Appender *appender1 = new log4cpp::OstreamAppender("console", &std::cout);
    appender1->setLayout(new log4cpp::BasicLayout());
    
    log4cpp::Appender *appender2 = new log4cpp::FileAppender("default", "program.log");
    appender2->setLayout(new log4cpp::BasicLayout());
    
    // 2. 创建记录器并配置
    log4cpp::Category& root = log4cpp::Category::getRoot();
    root.setPriority(log4cpp::Priority::WARN);
    root.addAppender(appender1);
    
    log4cpp::Category& sub1 = log4cpp::Category::getInstance(std::string("sub1"));
    sub1.addAppender(appender2);
    
    // 3. 使用日志记录
    root.error("root error");
    root.info("root info");  // 不会输出（优先级低于WARN）
    sub1.error("sub1 error");
    sub1.warn("sub1 warn");
    
    return 0;
}
````

**代码解析**：
1. **创建输出器**：分别创建控制台和文件输出器
2. **设置布局**：每个输出器都需要设置布局（一对一关系）
3. **创建记录器**：root记录器和子记录器
4. **配置优先级**：设置过滤级别
5. **添加输出器**：将输出器绑定到记录器
6. **记录日志**：使用不同优先级记录日志

### 3.4 自定义日志格式
````cpp path=4.日志系统.md mode=EXCERPT
PatternLayout * ptn1 = new PatternLayout();
ptn1->setConversionPattern("%d %c [%p] %m%n");
// %d: 时间  %c: 模块名  %p: 优先级  %m: 消息  %n: 换行
````

### 3.5 Category对象的创建方式
````cpp path=4.日志系统.md mode=EXCERPT
// 方式一：先创建根对象，再创建子对象
log4cpp::Category& root = log4cpp::Category::getRoot();
log4cpp::Category& sub1 = log4cpp::Category::getInstance("sub1");

// 方式二：直接创建子对象
log4cpp::Category& sub1 = log4cpp::Category::getRoot().getInstance("salesDepart");
````

## 4. 学习要点和注意事项

### 4.1 日志生命周期⭐⭐⭐
1. **产生**：调用日志记录函数（如`info("log information")`）
2. **记录器处理**：获取时间、位置、线程信息等
3. **过滤器判断**：根据优先级决定是否记录
4. **格式化处理**：按设定格式格式化日志
5. **输出器输出**：将日志写入目标位置
6. **生命结束**：日志处理完成

### 4.2 回卷文件机制理解⚠️
- **空间限制**：避免日志文件无限增长
- **文件命名**：`wd.log` → `wd.log.1` → `wd.log.2` → ...
- **回卷过程**：新日志写入`wd.log`，满了后重命名，创建新的`wd.log`
- **文件数量**：参数为9时实际有10个文件（0-9）

### 4.3 重要注意事项⚠️
1. **布局设置**：每个Appender都必须设置Layout（一对一关系）
2. **优先级过滤**：只有日志优先级 ≥ 系统优先级才会输出
3. **继承关系**：子Category继承父Category的优先级和目的地
4. **资源管理**：使用`new`创建的对象需要手动释放
5. **动态库配置**：编译后可能需要配置动态库路径

### 4.4 编译和环境配置💡
1. **编译参数**：必须链接`-llog4cpp -lpthread`
2. **头文件路径**：`/usr/local/include/log4cpp`
3. **库文件路径**：`/usr/local/lib`
4. **动态库配置**：修改`/etc/ld.so.conf`并执行`ldconfig`

### 4.5 最佳实践建议
1. **命名统一**：Category名称与变量名保持一致
2. **优先级设置**：根据实际需求合理设置过滤级别
3. **格式定制**：使用PatternLayout提高日志可读性
4. **文件管理**：大量日志使用RollingFileAppender

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **四大组件**：记录器(Category)、过滤器(Priority)、格式化器(Layout)、输出器(Appender)
2. **日志生命周期**：产生→记录器→过滤器→格式化器→输出器→结束
3. **优先级规则**：数值小=优先级高，日志优先级≥系统优先级才输出
4. **继承机制**：子Category继承父Category的配置

### ⚠️ 易错点提醒
1. **忘记设置Layout**：每个Appender必须设置Layout
2. **优先级理解错误**：数值越小优先级越高
3. **动态库配置**：编译后找不到库文件
4. **回卷文件数量**：参数N实际产生N+1个文件

### 💡 实用技巧
1. **格式化字符串**：`%d %c [%p] %m%n`（时间 模块 优先级 消息 换行）
2. **编译命令**：`g++ file.cc -llog4cpp -lpthread`
3. **动态库配置**：`echo "/usr/local/lib" >> /etc/ld.so.conf && ldconfig`
4. **优先级设置**：开发时用DEBUG，生产时用WARN或ERROR

### 🎯 常考知识点
1. **四大组件的作用**和相互关系
2. **日志优先级的数值**和过滤规则
3. **三种Appender的区别**和使用场景
4. **PatternLayout的格式化字符**含义
5. **回卷文件的工作机制**

### 📝 代码模板
```cpp
// 基本使用模板
log4cpp::Appender *appender = new log4cpp::FileAppender("name", "file.log");
appender->setLayout(new log4cpp::PatternLayout("%d %c [%p] %m%n"));

log4cpp::Category& logger = log4cpp::Category::getRoot();
logger.setPriority(log4cpp::Priority::INFO);
logger.addAppender(appender);

logger.info("This is an info message");
```

### 🔧 环境配置清单
1. ✅ 安装log4cpp库
2. ✅ 配置动态库路径
3. ✅ 编译时链接正确的库
4. ✅ 包含必要的头文件

这份笔记涵盖了log4cpp日志系统的核心内容，重点掌握四大组件的概念和使用方法，理解日志的生命周期和优先级过滤机制，这些是实际项目开发中必备的技能。