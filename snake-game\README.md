# 🐍 现代化贪吃蛇游戏

一个使用 HTML5 Canvas + JavaScript 实现的现代化贪吃蛇游戏，具有精美的界面设计和丰富的游戏功能。

## ✨ 功能特色

### 🎮 核心游戏功能
- **经典贪吃蛇玩法**: 控制蛇移动，吃食物增长，避免撞墙和撞自己
- **多种难度级别**: 简单、中等、困难三种速度选择
- **实时分数统计**: 当前分数、最高分数、游戏时间显示
- **本地存储**: 自动保存最高分记录

### 🌟 增强功能
- **特殊食物系统**:
  - 🍎 红色普通食物: +10分
  - ⭐ 金色减速食物: +50分，临时减速5秒
  - 💎 蓝色穿墙食物: +30分，可穿墙5秒
- **障碍物模式**: 可选开启随机障碍物增加挑战
- **音效系统**: 吃食物、特殊效果、游戏结束音效
- **暂停/继续**: 支持游戏暂停和恢复

### 📱 控制方式
- **键盘控制**: 方向键或WASD键控制移动
- **触屏控制**: 移动端专用方向键界面
- **快捷键**: 空格键暂停/继续游戏

### 🎨 界面设计
- **现代扁平化设计**: 渐变色彩和霓虹效果
- **响应式布局**: 完美适配桌面和移动设备
- **动画效果**: 按钮悬停、分数闪烁、食物脉动
- **游戏覆盖层**: 精美的开始、暂停、结束界面

## 🚀 快速开始

### 1. 下载项目
```bash
# 下载项目文件到本地
git clone [项目地址] 或直接下载ZIP文件
```

### 2. 运行游戏
```bash
# 方法1: 直接双击 index.html 文件
# 方法2: 使用本地服务器（推荐）
python -m http.server 8000
# 然后访问 http://localhost:8000
```

### 3. 开始游戏
1. 打开浏览器访问游戏页面
2. 选择难度级别
3. 点击"开始游戏"按钮
4. 使用方向键或触屏控制蛇的移动

## 🎯 游戏规则

### 基础规则
- 控制蛇移动吃食物
- 每吃一个食物蛇身增长一节
- 撞墙或撞到自己身体游戏结束
- 分数越高越好

### 特殊食物效果
- **金色食物**: 获得高分并临时减速，更容易控制
- **蓝色食物**: 获得中等分数并可穿墙，避免撞墙死亡
- **普通食物**: 基础分数，稳定增长

### 难度说明
- **简单**: 移动速度慢，适合新手
- **中等**: 标准速度，平衡挑战
- **困难**: 高速移动，考验反应

## 🛠️ 技术实现

### 技术栈
- **HTML5**: 语义化结构和Canvas画布
- **CSS3**: 现代化样式、动画、响应式设计
- **JavaScript ES6+**: 面向对象编程、模块化设计

### 核心架构
```javascript
// 主要类结构
class Snake {
    // 蛇的状态管理、移动逻辑、碰撞检测
}

class Food {
    // 食物生成、类型管理、特效处理
}

class Game {
    // 游戏主循环、状态管理、界面控制
}

class AudioManager {
    // 音效管理和播放控制
}
```

### 关键技术点

#### 1. 游戏循环系统
```javascript
gameLoop() {
    // 移动蛇 -> 检查碰撞 -> 处理食物 -> 重绘画面
    this.snake.move();
    if (this.checkCollisions()) return this.gameOver();
    if (this.checkFoodCollision()) this.handleFoodEaten();
    this.draw();
}
```

#### 2. 碰撞检测算法
- 墙体碰撞: 检查蛇头坐标是否超出边界
- 自身碰撞: 遍历蛇身检查是否与蛇头重叠
- 障碍物碰撞: 检查蛇头与障碍物位置

#### 3. 响应式设计
- CSS Grid和Flexbox布局
- 媒体查询适配不同屏幕尺寸
- 移动端触控优化

#### 4. 性能优化
- requestAnimationFrame替代setInterval
- 事件防抖处理
- 音频预加载

## 📁 项目结构

```
snake-game/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # 游戏逻辑
├── sounds/             # 音频文件夹
│   ├── eat.mp3         # 吃食物音效
│   ├── special.mp3     # 特殊效果音效
│   ├── game-over.mp3   # 游戏结束音效
│   └── README.md       # 音频说明
└── README.md           # 项目说明
```

## 🎵 音效配置

游戏支持音效，需要在 `sounds/` 文件夹中放置以下音频文件：
- `eat.mp3/wav`: 吃普通食物音效
- `special.mp3/wav`: 吃特殊食物音效  
- `game-over.mp3/wav`: 游戏结束音效

详细音效说明请查看 `sounds/README.md`

## 🔧 自定义配置

### 修改游戏参数
在 `script.js` 文件顶部的 `GAME_CONFIG` 对象中可以调整：

```javascript
const GAME_CONFIG = {
    CANVAS_SIZE: 600,        // 画布大小
    GRID_SIZE: 20,           // 网格大小
    INITIAL_SNAKE_LENGTH: 3, // 初始蛇长
    SPEEDS: {                // 难度速度
        easy: 150,
        medium: 100,
        hard: 60
    },
    COLORS: {                // 颜色配置
        snake: '#00ff88',
        food: '#ff6b6b',
        // ...更多颜色
    }
};
```

### 添加新功能
游戏采用模块化设计，可以轻松扩展：
- 在相应类中添加新方法
- 修改游戏循环逻辑
- 添加新的特殊食物类型
- 实现新的游戏模式

## 🌐 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 📱 移动端优化

- 触屏方向键控制
- 响应式界面适配
- 触摸事件优化
- 移动端性能调优

## 🐛 常见问题

### Q: 游戏无法加载？
A: 确保使用HTTP服务器运行，不要直接打开HTML文件

### Q: 没有音效？
A: 检查音频文件是否正确放置在sounds文件夹中

### Q: 移动端控制不灵敏？
A: 确保触摸事件没有被其他元素阻挡

### Q: 游戏卡顿？
A: 检查浏览器性能，关闭其他占用资源的标签页

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进游戏：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 开源协议

本项目采用 MIT 协议开源，详情请查看 LICENSE 文件。

## 👨‍💻 作者信息

- 开发者: AI Assistant
- 技术栈: HTML5 + CSS3 + JavaScript ES6+
- 设计理念: 现代化、响应式、用户友好

---

🎮 **享受游戏，挑战高分！** 🎮
