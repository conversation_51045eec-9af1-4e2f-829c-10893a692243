# C++类与对象 学习笔记

## 1. 主要知识点概括和解释

### 1.1 面向对象思想基础

#### 过程论 vs 对象论
- **过程论（面向过程）**：数据和逻辑分离，程序本质是过程
  - 特点：确定性强，适合简单问题
  - 局限：复杂系统难以理清思路

- **对象论（面向对象）**：数据和逻辑相互依存，形成对象
  - 特点：相对不确定，但便于分离关注
  - 优势：**更适合分析规模较大的事物**

#### 类与对象的关系
- **类**：对现实世界相似事物的抽象（模板）
- **对象**：类的实例（具体的变量）
- **关系**：由对象抽象出类，由类实例化出对象

### 1.2 类的基本组成

````cpp path=2.类与对象.md mode=EXCERPT
class MyClass {
public:
    void myFunc() {}  // 成员函数
private:
    int _a;          // 数据成员
}; // 分号不能省略
````

**类的组成**：
1. **数据成员**：相当于现实世界中的属性
2. **成员函数**：相当于现实世界中的行为

**访问权限控制**：
- **public**：类外可访问
- **private**：类外不可访问（默认）
- **protected**：继承时使用

## 2. 重要概念定义和区别

### 2.1 构造函数（Constructor）⭐⭐⭐

#### 构造函数的作用
- **目的**：完成数据成员的初始化及其他操作
- **调用时机**：对象创建时自动调用
- **特点**：函数名与类名相同，无返回值

#### 对象创建规则

| 情况               | 编译器行为           | 注意事项               |
| ------------------ | -------------------- | ---------------------- |
| **无显式构造函数** | 自动生成默认构造函数 | 不会初始化数据成员     |
| **有显式构造函数** | 不再生成默认构造函数 | 必须提供合适的构造方式 |
| **多个构造函数**   | 支持重载             | 提供多种创建对象的方式 |

#### 初始化列表（推荐方式）⭐

````cpp path=2.类与对象.md mode=EXCERPT
Point(int ix = 0, int iy = 0)
: _ix(ix)    // 初始化列表
, _iy(iy)
{
    cout << "Point(int,int)" << endl;
}
````

**初始化列表 vs 函数体赋值**：

| 特性       | 初始化列表                | 函数体赋值         |
| ---------- | ------------------------- | ------------------ |
| **性质**   | 真正的初始化              | 先默认初始化再赋值 |
| **效率**   | 更高效                    | 相对低效           |
| **必要性** | const成员、引用成员必须用 | 普通成员可选       |
| **推荐度** | **强烈推荐**              | 不推荐             |

### 2.2 析构函数（Destructor）⭐⭐⭐

#### 析构函数的特点
- **目的**：回收数据成员申请的资源
- **调用时机**：对象销毁时自动调用
- **特点**：函数名为`~类名`，无参数，无返回值

#### 析构函数调用时机

| 对象类型     | 析构调用时机     |
| ------------ | ---------------- |
| **全局对象** | 整个程序结束时   |
| **局部对象** | 离开作用域时     |
| **静态对象** | 整个程序结束时   |
| **堆对象**   | 使用delete删除时 |

````cpp path=2.类与对象.md mode=EXCERPT
class Computer {
public:
    Computer(const char * brand, double price)
    : _brand(new char[strlen(brand) + 1]())
    , _price(price)
    {
        strcpy(_brand, brand);
    }
    
    ~Computer() {
        if(_brand) {
            delete [] _brand;
            _brand = nullptr;
        }
    }
    
private:
    char * _brand;
    double _price;
};
````

### 2.3 对象的复制⭐⭐⭐

#### 拷贝构造函数

````cpp path=2.类与对象.md mode=EXCERPT
Point(const Point & rhs)  // 拷贝构造函数
: _ix(rhs._ix)
, _iy(rhs._iy)
{
    cout << "Point(const Point &)" << endl;
}
````

**拷贝构造函数调用时机**：
1. 用已存在对象初始化新对象
2. 函数参数为对象（值传递）
3. 函数返回对象（值返回）

#### 赋值运算符函数

````cpp path=2.类与对象.md mode=EXCERPT
Point & operator=(const Point & rhs) {
    if(this != &rhs) {  // 自赋值检测
        _ix = rhs._ix;
        _iy = rhs._iy;
    }
    return *this;
}
````

#### 拷贝构造 vs 赋值运算符

| 特性           | 拷贝构造函数       | 赋值运算符函数 |
| -------------- | ------------------ | -------------- |
| **调用时机**   | 对象创建时         | 对象已存在时   |
| **语法形式**   | `Point pt2 = pt1;` | `pt2 = pt1;`   |
| **返回值**     | 无返回值           | 返回引用       |
| **自赋值检测** | 不需要             | 必须检测       |

### 2.4 三合成原则⭐⭐⭐

**如果需要手动定义其中一个，另外两个也需要手动定义**：
1. **拷贝构造函数**
2. **赋值运算符函数**  
3. **析构函数**

**原因**：通常需要手动定义这些函数的类都涉及资源管理（如动态内存），必须确保资源的正确复制和释放。

### 2.5 特殊的数据成员

#### 常量数据成员

````cpp path=2.类与对象.md mode=EXCERPT
class Point {
private:
    const int _ix;  // 常量成员
    const int _iy;
public:
    Point(int ix, int iy)
    : _ix(ix)  // 必须在初始化列表中初始化
    , _iy(iy)
    {}
};
````

#### 引用数据成员

````cpp path=2.类与对象.md mode=EXCERPT
class Point {
private:
    int _ix;
    int _iy;
    int & _iz;  // 引用成员
public:
    Point(int ix, int iy)
    : _ix(ix)
    , _iy(iy)
    , _iz(_ix)  // 必须在初始化列表中初始化
    {}
};
````

#### 对象成员

````cpp path=2.类与对象.md mode=EXCERPT
class Line {
public:
    Line(int x1, int y1, int x2, int y2)
    : _pt1(x1, y1)  // 对象成员初始化
    , _pt2(x2, y2)
    {
        cout << "Line(int,int,int,int)" << endl;
    }
private:
    Point _pt1;  // 对象成员
    Point _pt2;
};
````

### 2.6 const成员函数⭐⭐

#### const成员函数的特点

````cpp path=2.类与对象.md mode=EXCERPT
class Point {
public:
    void print() const {  // const成员函数
        cout << "(" << _ix << "," << _iy << ")" << endl;
        // _ix = 10;  // error: 不能修改数据成员
    }
private:
    int _ix;
    int _iy;
};
````

**特点**：
1. 不能修改对象的数据成员
2. this指针被设置为双重const限定
3. const对象只能调用const成员函数

#### 使用规则
1. const对象调用const成员函数
2. 非const对象优先调用非const版本
3. **建议**：确定不修改数据成员的函数都定义为const

## 3. 关键代码示例说明

### 3.1 完整的类定义示例

````cpp path=2.类与对象.md mode=EXCERPT
class Computer {
public:
    // 构造函数
    Computer(const char * brand, double price)
    : _brand(new char[strlen(brand) + 1]())
    , _price(price)
    {
        strcpy(_brand, brand);
    }
    
    // 拷贝构造函数
    Computer(const Computer & rhs)
    : _brand(new char[strlen(rhs._brand) + 1]())
    , _price(rhs._price)
    {
        strcpy(_brand, rhs._brand);
    }
    
    // 赋值运算符函数
    Computer & operator=(const Computer & rhs) {
        if(this != &rhs) {
            delete [] _brand;
            _brand = new char[strlen(rhs._brand) + 1]();
            strcpy(_brand, rhs._brand);
            _price = rhs._price;
        }
        return *this;
    }
    
    // 析构函数
    ~Computer() {
        if(_brand) {
            delete [] _brand;
            _brand = nullptr;
        }
    }
    
    // const成员函数
    void print() const {
        cout << "brand: " << _brand << ", price: " << _price << endl;
    }
    
private:
    char * _brand;
    double _price;
};
````

### 3.2 单例模式实现⭐⭐

#### 堆区单例模式（推荐）

````cpp path=2.类与对象.md mode=EXCERPT
class Point {
public:
    static Point * getInstance() {
        if(_pInstance == nullptr) {
            _pInstance = new Point(1, 2);
        }
        return _pInstance;
    }
    
    static void destroy() {
        if(_pInstance) {
            delete _pInstance;
            _pInstance = nullptr;
        }
    }
    
    void print() const {
        cout << "(" << _ix << "," << _iy << ")" << endl;
    }
    
private:
    Point(int x, int y) : _ix(x), _iy(y) {}
    ~Point() {}
    Point(const Point &) = delete;
    Point & operator=(const Point &) = delete;
    
    int _ix;
    int _iy;
    static Point * _pInstance;
};

Point * Point::_pInstance = nullptr;  // 静态成员定义
````

### 3.3 new/delete表达式工作步骤

#### new表达式的三个步骤
1. 调用operator new申请未类型化的空间
2. 在该空间上调用构造函数初始化对象
3. 返回指向该对象的相应类型的指针

#### delete表达式的两个步骤
1. 调用析构函数，回收数据成员申请的资源
2. 调用operator delete回收本对象所在的空间

````cpp path=2.类与对象.md mode=EXCERPT
// 默认的operator new
void * operator new(size_t sz) {
    void * ret = malloc(sz);
    return ret;
}

// 默认的operator delete
void operator delete(void * p) {
    free(p);
}
````

## 4. 学习要点和注意事项

### 4.1 类定义注意事项⚠️
1. **类定义后必须加分号**
2. **数据成员建议以下划线开头**（如`_name`）
3. **访问权限默认为private**
4. **成员函数可以在类内定义（自动内联）或类外定义**

### 4.2 构造函数注意事项⚠️
1. **构造函数无返回值**，连void都不写
2. **推荐使用初始化列表**进行数据成员初始化
3. **初始化顺序与声明顺序一致**，与初始化列表顺序无关
4. **const成员、引用成员、对象成员必须在初始化列表中初始化**

### 4.3 析构函数注意事项⚠️
1. **析构函数无参数，无返回值**
2. **有指针成员时必须在析构函数中释放内存**
3. **析构函数中要检查指针是否为空**
4. **释放后将指针置为nullptr**

### 4.4 拷贝控制注意事项⚠️
1. **有指针成员时要实现深拷贝**
2. **赋值运算符函数必须检测自赋值**
3. **赋值运算符函数要返回*this的引用**
4. **遵循三合成原则**

### 4.5 const成员函数注意事项⚠️
1. **不修改数据成员的函数应定义为const**
2. **const对象只能调用const成员函数**
3. **const成员函数不能调用非const成员函数**

### 4.6 内存管理注意事项⚠️
1. **new/delete必须配对使用**
2. **new[]对应delete[]**
3. **避免重复delete**
4. **注意内存泄漏和悬空指针**

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **类与对象的关系**：类是模板，对象是实例
2. **构造函数的作用和调用时机**
3. **析构函数的作用和调用时机**
4. **初始化列表的使用方法**（推荐方式）
5. **拷贝构造函数和赋值运算符函数的区别**
6. **三合成原则**的内容和重要性
7. **const成员函数的特点和使用规则**
8. **单例模式的实现方法**

### ⚠️ 易错点提醒
1. **类定义后忘记加分号**
2. **构造函数写了返回值**
3. **析构函数写了参数**
4. **忘记在析构函数中释放动态内存**
5. **赋值运算符函数忘记检测自赋值**
6. **const成员、引用成员没有在初始化列表中初始化**
7. **拷贝构造函数参数忘记加const和引用**

### 💡 最佳实践
1. **优先使用初始化列表初始化数据成员**
2. **有指针成员时要实现深拷贝**
3. **不修改数据成员的函数定义为const**
4. **遵循三合成原则**
5. **合理使用单例模式管理全局资源**
6. **数据成员设为private，通过public成员函数访问**

### 🎯 面试常考
1. **构造函数和析构函数的调用时机**
2. **拷贝构造函数的调用场景**
3. **三合成原则的内容**
4. **const成员函数的作用**
5. **单例模式的实现方式**
6. **new/delete表达式的工作步骤**
7. **内存对齐的原理和目的**

### 📊 对象内存布局

#### 对象大小计算
- **成员函数不影响对象大小**
- **对象大小与数据成员有关**
- **存在内存对齐机制**

#### 内存对齐规则
- **规则**：按照类中占空间最大的数据成员大小的倍数对齐
- **目的**：提高CPU访问效率
- **代价**：可能浪费一些空间

````cpp path=2.类与对象.md mode=EXCERPT
class A {
    int _num;      // 4字节
    double _price; // 8字节
};
// sizeof(A) = 16 (按8字节对齐)

class B {
    int _num;   // 4字节
    int _price; // 4字节
};
// sizeof(B) = 8 (按4字节对齐)
````

### 🏗️ 对象的组织方式

#### const对象
````cpp path=2.类与对象.md mode=EXCERPT
const Point pt(1,2);  // const对象
pt.print();  // 只能调用const成员函数
````

#### 指向对象的指针
````cpp path=2.类与对象.md mode=EXCERPT
Point * p1 = &pt;        // 指向栈对象
Point * p2 = new Point(3,4);  // 指向堆对象
p1->print();  // 通过指针调用成员函数
````

#### 对象数组
````cpp path=2.类与对象.md mode=EXCERPT
Point pts[2] = {Point(1,2), Point(3,4)};  // 对象数组
Point pts[] = {{1,2}, {3,4}};  // 简化写法
````

### 📝 语法速查

#### 类的基本结构
````cpp path=2.类与对象.md mode=EDIT
class ClassName {
public:
    ClassName();                    // 默认构造函数
    ClassName(const ClassName &);  // 拷贝构造函数
    ClassName & operator=(const ClassName &); // 赋值运算符
    ~ClassName();                   // 析构函数
    
    void func() const;              // const成员函数
    
private:
    int _data;                      // 数据成员
};
````

#### 初始化列表语法
````cpp path=2.类与对象.md mode=EDIT
ClassName(int x, int y)
: _x(x)      // 初始化列表
, _y(y)
, _z(0)
{
    // 构造函数体
}
````

#### 单例模式模板
````cpp path=2.类与对象.md mode=EDIT
class Singleton {
public:
    static Singleton * getInstance();
    static void destroy();
    
private:
    Singleton();
    ~Singleton();
    Singleton(const Singleton &) = delete;
    Singleton & operator=(const Singleton &) = delete;
    
    static Singleton * _pInstance;
};
````

### 🌟 学习建议
1. **理论与实践结合**：每个概念都要写代码验证
2. **注重细节**：C++语法严格，细节很重要
3. **多画内存图**：理解对象在内存中的布局
4. **掌握调试技巧**：使用gdb调试，观察对象创建销毁过程
5. **循序渐进**：先掌握基础语法，再学习设计模式

### 🎓 进阶方向
掌握类与对象基础后，可以学习：
- **运算符重载**：让自定义类型支持运算符操作
- **继承**：代码复用和层次化设计
- **多态**：虚函数和动态绑定
- **模板**：泛型编程
- **STL容器**：标准库的使用
- **智能指针**：现代C++的内存管理

### 📚 重要概念总结表

| 概念              | 作用       | 调用时机     | 注意事项             |
| ----------------- | ---------- | ------------ | -------------------- |
| **构造函数**      | 初始化对象 | 对象创建时   | 无返回值，可重载     |
| **析构函数**      | 清理资源   | 对象销毁时   | 无参数无返回值       |
| **拷贝构造**      | 复制对象   | 对象初始化时 | 参数为const引用      |
| **赋值运算符**    | 对象赋值   | 对象已存在时 | 检测自赋值，返回引用 |
| **const成员函数** | 只读访问   | 不修改数据时 | const对象只能调用    |

这份笔记涵盖了C++类与对象的核心概念，是面向对象编程的重要基础。建议重点掌握构造函数、析构函数、拷贝控制和const成员函数的使用，这些是后续学习的基石。