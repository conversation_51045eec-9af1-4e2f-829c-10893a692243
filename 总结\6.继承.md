# 继承 学习笔记

## 1. 主要知识点概括

### 1.1 继承的基本概念
- **定义**：在原有类型基础上定义新类型，新类型包含原有类型的成员并可添加新成员
- **目的**：代码重用，避免重复编写相同代码
- **术语**：原有类型称为"基类"或"父类"，新类型称为"派生类"或"子类"
- **本质**：模拟现实世界中的层次抽象关系

### 1.2 派生类定义过程
1. **吸收基类成员**：自动获得基类的所有成员
2. **添加新成员**：可以定义自己特有的成员（非必须）
3. **隐藏基类成员**：可以重新定义同名成员（非必须）

### 1.3 继承的分类
- **单继承**：一个派生类只有一个直接基类
- **多重继承**：一个派生类有多个直接基类
- **虚拟继承**：解决菱形继承问题的特殊继承方式

## 2. 重要概念辨析

### 2.1 三种继承方式对比

````cpp path=6.继承.md mode=EXCERPT
class 派生类 : public/protected/private 基类 {};
````

| 继承方式      | 基类public成员 | 基类protected成员 | 基类private成员 | 类外访问                 | 特点                   |
| ------------- | -------------- | ----------------- | --------------- | ------------------------ | ---------------------- |
| **public**    | **public**     | **protected**     | **不可访问**    | **基类public成员可访问** | **接口继承，IS-A关系** |
| **protected** | **protected**  | **protected**     | **不可访问**    | **都不可访问**           | **实现继承，可传递**   |
| **private**   | **private**    | **private**       | **不可访问**    | **都不可访问**           | **实现继承，切断传递** |

### 2.2 访问权限规则总结⭐⭐⭐

**派生类内部访问规则**：
1. **私有成员**：任何继承方式都无法访问基类私有成员
2. **非私有成员**：任何继承方式都可以访问基类的公有和保护成员

**派生类外部访问规则**：
- 只有**公有继承的基类公有成员**可以通过派生类对象访问

**记忆口诀**：继承方式和基类成员访问权限取交集

### 2.3 保护继承vs私有继承的关键区别

````cpp path=6.继承.md mode=EXCERPT
class A { public: int a; };
class B : private A {};
class C : private B {
    void func(){ a; //error，无法访问a }
};
````

| 特性             | 保护继承               | 私有继承               |
| ---------------- | ---------------------- | ---------------------- |
| **多层继承传递** | **可以传递访问权限**   | **切断访问链**         |
| **使用场景**     | 实现继承，保持层次关系 | 实现继承，隐藏基类接口 |
| **典型应用**     | 框架设计中的中间层     | 组合关系的实现         |

### 2.4 继承关系的局限性

**不能被继承的内容**：
- **构造函数和析构函数**：对象创建销毁方式
- **拷贝构造和赋值运算符**：复制控制方式  
- **operator new/delete**：空间分配方式
- **友元关系**：为降低封装性破坏的影响

## 3. 关键代码示例说明

### 3.1 基本继承语法

````cpp path=6.继承.md mode=EXCERPT
class Base {
public:
    Base(long base) : _base(base) {}
    void display() { cout << "Base::display() " << _base << endl; }
    long _base;
};

class Derived : public Base {
public:
    Derived(long base, long derived)
    : Base(base)  // 显式调用基类构造函数
    , _derived(derived)
    { cout << "Derived(long)" << endl; }
    
    void display() { cout << "Derived::display() " << _derived << endl; }
    long _derived;
};
````

**说明**：
- 派生类构造函数必须在初始化列表中调用基类构造函数
- 派生类的`display`函数隐藏了基类的同名函数

### 3.2 构造和析构顺序

**构造顺序**：
1. 调用派生类构造函数
2. 在初始化列表开始时调用基类构造函数
3. 初始化派生类成员
4. 执行派生类构造函数体

**析构顺序**：
1. 调用派生类析构函数
2. 派生类析构函数执行完毕
3. 自动调用基类析构函数

````cpp path=6.继承.md mode=EXCERPT
void test() {
    Derived d(1, 2);  // 先调用Base构造，再调用Derived构造
}  // 先调用Derived析构，再调用Base析构
````

### 3.3 成员隐藏机制

````cpp path=6.继承.md mode=EXCERPT
class Base {
public:
    long _data = 100;
    void print() const { cout << "Base::print()" << endl; }
};

class Derived : public Base {
public:
    long _data = 19;  // 隐藏基类的_data
    void print(int x) const { cout << "Derived::print(int)" << endl; }  // 隐藏基类的print
};

void test() {
    Derived dd;
    cout << dd._data << endl;        // 输出19（派生类成员）
    cout << dd.Base::_data << endl;  // 输出100（基类成员）
    
    dd.print(1);      // 调用派生类版本
    dd.Base::print(); // 调用基类版本
}
````

**隐藏规则**：
- **数据成员隐藏**：同名数据成员会隐藏基类成员
- **函数隐藏**：同名函数会隐藏基类函数（无论参数是否相同）

### 3.4 多重继承语法

````cpp path=6.继承.md mode=EXCERPT
class A { public: void printA() { cout << "A" << endl; } };
class B { public: void printB() { cout << "B" << endl; } };
class C { public: void printC() { cout << "C" << endl; } };

class D : public A, public B, public C {
public:
    D() { cout << "D()" << endl; }
    ~D() { cout << "~D()" << endl; }
};
````

**构造析构顺序**：
- **构造**：按继承声明顺序调用（A→B→C→D）
- **析构**：按继承声明顺序逆序调用（D→C→B→A）

### 3.5 菱形继承问题及虚拟继承解决方案

**问题代码**：
````cpp path=6.继承.md mode=EXCERPT
class A { public: void print() { cout << "A::print()" << endl; } };
class B : public A {};
class C : public A {};
class D : public B, public C {
    void test() {
        print();  // error: 二义性，不知道调用哪个A的print
    }
};
````

**解决方案**：
````cpp path=6.继承.md mode=EXCERPT
class B : virtual public A {};  // 虚拟继承
class C : virtual public A {};  // 虚拟继承
class D : public B, public C {
    void test() {
        print();  // ok: 只有一个A类子对象
    }
};
````

### 3.6 基类与派生类间的转换

**向上转型（安全）**：
````cpp path=6.继承.md mode=EXCERPT
Base base(1);
Derived d1(2, 5);

base = d1;           // ✓ 派生类对象赋值给基类对象
Base* pbase = &d1;   // ✓ 基类指针指向派生类对象  
Base& rbase = d1;    // ✓ 基类引用绑定派生类对象
````

**向下转型（需要转换）**：
````cpp path=6.继承.md mode=EXCERPT
Base* pbase = &d1;
// Derived* pd = pbase;  // error: 直接转换不允许

// C风格转换（不安全）
Derived* pd1 = (Derived*)pbase;

// dynamic_cast转换（安全）
Derived* pd2 = dynamic_cast<Derived*>(pbase);
if (pd2) {
    cout << "转换成功" << endl;
} else {
    cout << "转换失败" << endl;
}
````

### 3.7 派生类的复制控制

````cpp path=6.继承.md mode=EXCERPT
class Derived : public Base {
public:
    // 拷贝构造函数
    Derived(const Derived& other)
    : Base(other)  // 显式调用基类拷贝构造
    , _pdata(new char[strlen(other._pdata) + 1])
    {
        strcpy(_pdata, other._pdata);
    }
    
    // 赋值运算符
    Derived& operator=(const Derived& rhs) {
        if (this != &rhs) {
            Base::operator=(rhs);  // 显式调用基类赋值运算符
            delete[] _pdata;
            _pdata = new char[strlen(rhs._pdata) + 1];
            strcpy(_pdata, rhs._pdata);
        }
        return *this;
    }
    
private:
    char* _pdata;
};
````

**复制控制原则**：
- **未显式定义**：编译器自动处理基类部分
- **显式定义**：必须手动调用基类的复制控制函数

## 4. 学习要点和注意事项

### 4.1 继承方式选择指南⭐⭐⭐

**公有继承（public）**：
- **使用场景**：IS-A关系，接口继承
- **特点**：派生类对象可以当作基类对象使用
- **典型应用**：形状类→圆形类、动物类→狗类

**保护继承（protected）**：
- **使用场景**：实现继承，需要在继承层次中传递访问权限
- **特点**：外部无法访问，但可以继续派生
- **典型应用**：框架设计中的中间层类

**私有继承（private）**：
- **使用场景**：实现继承，组合关系的实现
- **特点**：切断继承链，外部无法访问基类接口
- **典型应用**：用继承实现组合关系

### 4.2 构造函数调用机制⚠️

**三种情况**：
1. **自动调用**：派生类未显式调用时，自动调用基类默认构造函数
2. **编译错误**：基类无默认构造函数且派生类未显式调用
3. **显式调用**：在初始化列表中指定调用哪个基类构造函数

**重要提醒**：
- 创建派生类对象时，一定先调用派生类构造函数
- 在派生类构造函数执行过程中调用基类构造函数

### 4.3 多重继承的问题⚠️

**成员名访问冲突**：
- **问题**：多个基类有同名成员
- **解决**：使用作用域解析符（不推荐）或在派生类中重新定义

**存储二义性（菱形继承）**：
- **问题**：最底层派生类包含多个顶层基类副本
- **解决**：使用虚拟继承（`virtual public`）

### 4.4 虚拟继承的特点💡

**内存布局变化**：
- 增加虚基类指针（vbptr）
- 基类内容放在内存空间末尾
- 只保留一份基类子对象

**构造函数调用**：
- 最底层派生类负责调用虚基类构造函数
- 中间层的基类构造调用被"压抑"

### 4.5 类型转换安全性⚠️

**向上转型安全原因**：
- 派生类对象包含完整的基类子对象
- 基类指针只操作基类部分，不会越界

**向下转型危险原因**：
- 基类对象不包含派生类特有成员
- 强制转换可能导致访问非法内存

**推荐做法**：
- 优先使用`dynamic_cast`进行安全的向下转型
- 避免使用C风格强制转换

## 5. 便于复习的要点总结

### 🔥 核心概念必掌握
1. **三种继承方式**：public（接口继承）、protected（实现继承，可传递）、private（实现继承，切断传递）
2. **访问权限规则**：继承方式与基类成员权限取交集
3. **构造析构顺序**：构造时先基类后派生类，析构时先派生类后基类
4. **成员隐藏机制**：同名成员隐藏基类成员，函数隐藏与参数无关

### ⚠️ 重要规则记忆
1. **不能继承的内容**：构造析构、复制控制、new/delete、友元
2. **菱形继承问题**：用虚拟继承解决存储二义性
3. **类型转换规则**：向上安全，向下需要转换
4. **复制控制原则**：显式定义时必须手动调用基类版本

### 💡 常考知识点速记
```
访问权限：私有成员永远不可访问，其他取交集
构造顺序：派生类构造→基类构造→派生类成员初始化
析构顺序：派生类析构→基类析构
隐藏规则：同名即隐藏，与参数无关
转换规则：向上安全，向下危险
```

### 🎯 典型考试题型
1. **访问权限判断**：给定继承方式，判断成员是否可访问
2. **构造析构顺序**：多层继承时的调用顺序
3. **成员隐藏问题**：同名成员的访问方式
4. **菱形继承**：虚拟继承的作用和内存布局
5. **类型转换**：向上向下转型的安全性

### 📝 代码模板记忆

**基本继承**：
```cpp
class Derived : public Base {
public:
    Derived(params) : Base(base_params), member(value) {}
};
```

**复制控制**：
```cpp
Derived(const Derived& other) : Base(other) { /*派生类部分*/ }
Derived& operator=(const Derived& rhs) {
    Base::operator=(rhs); /*派生类部分*/ return *this;
}
```

**虚拟继承**：
```cpp
class B : virtual public A {};
class C : virtual public A {};
```

**安全向下转型**：
```cpp
Derived* pd = dynamic_cast<Derived*>(pbase);
if (pd) { /*转换成功*/ }
```

### 🔧 实用技巧
1. **优先使用public继承**：符合面向对象设计原则
2. **避免深层继承**：超过3层继承通常设计有问题
3. **谨慎使用多重继承**：容易产生复杂性和二义性
4. **使用dynamic_cast**：进行安全的类型转换
5. **遵循IS-A原则**：public继承应该表示"是一个"的关系

### 📚 学习顺序建议
1. 理解继承的基本概念和语法
2. 掌握三种继承方式的访问权限
3. 学习构造析构的调用机制
4. 理解成员隐藏和类型转换
5. 掌握多重继承和虚拟继承
6. 学习复制控制的正确实现

### 🎓 面试重点
- 解释三种继承方式的区别和使用场景
- 说明菱形继承问题及虚拟继承解决方案
- 分析构造析构函数的调用顺序
- 解释为什么某些内容不能被继承
- 讨论继承与组合的选择原则

这份笔记涵盖了C++继承机制的核心内容，重点掌握访问权限规则、构造析构机制、多重继承问题和类型转换，这些是面向对象编程的基础技能。