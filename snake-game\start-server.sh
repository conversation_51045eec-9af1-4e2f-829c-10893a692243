#!/bin/bash

echo "正在启动本地服务器..."
echo ""
echo "请确保已安装Python 3.x"
echo ""
echo "服务器启动后，请在浏览器中访问："
echo "http://localhost:8000"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 尝试使用Python 3
if command -v python3 &> /dev/null; then
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    # 检查Python版本
    python_version=$(python --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1)
    if [ "$python_version" = "3" ]; then
        python -m http.server 8000
    else
        python -m SimpleHTTPServer 8000
    fi
else
    echo ""
    echo "错误：未找到Python！"
    echo "请安装Python 3.x"
    echo ""
    echo "或者您可以："
    echo "1. 直接双击 index.html 文件"
    echo "2. 使用其他本地服务器"
    echo ""
fi
